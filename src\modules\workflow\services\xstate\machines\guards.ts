import { WorkflowContext, NodeExecutionContext, DetailedNodeExecutionResult } from '../types';

/**
 * Workflow machine guards
 */
export const workflowGuards = {
  /**
   * Check if workflow validation was successful
   */
  isValidationSuccessful: ({ event }: { event: any }) => {
    return Boolean(event?.output && event.output.isValid);
  },

  /**
   * Check if there are ready nodes to execute
   */
  hasReadyNodes: ({ context }: { context: WorkflowContext }) => {
    return Boolean(context?.readyNodes && context.readyNodes.length > 0);
  },

  /**
   * Check if there are currently running nodes
   */
  hasRunningNodes: ({ context }: { context: WorkflowContext }) => {
    return Boolean(context?.runningNodes && context.runningNodes.length > 0);
  },

  /**
   * Check if all nodes have been completed or failed
   */
  allNodesCompleted: ({ context }: { context: WorkflowContext }) => {
    const totalNodes = context?.nodes ? context.nodes.size : 0;
    const completedNodes = context?.metadata?.completedNodes || 0;
    const failedNodes = context?.metadata?.failedNodes || 0;
    // Note: cancelledNodes is not in WorkflowExecutionMetadata interface

    return Boolean(totalNodes > 0 && (completedNodes + failedNodes) >= totalNodes);
  },

  /**
   * Check if workflow has any critical errors that should stop execution
   */
  hasCriticalErrors: ({ context }: { context: WorkflowContext }) => {
    if (!context?.errors || context.errors.size === 0) {
      return false;
    }

    // Check for critical error types
    const criticalErrorTypes = ['validation', 'dependency_cycle', 'workflow_load'];

    try {
      for (const [errorKey, error] of context.errors.entries()) {
        if (criticalErrorTypes.some(type => errorKey.includes(type))) {
          return true;
        }
      }
    } catch (error) {
      // Safe fallback if iteration fails
      return false;
    }

    return false;
  },

  /**
   * Check if workflow can be retried
   */
  canRetryWorkflow: ({ context }: { context: WorkflowContext }) => {
    const maxRetries = (context.options as any)?.maxRetries || 3;
    const currentRetries = context.metadata?.retryCount || 0;

    return currentRetries < maxRetries;
  },

  /**
   * Check if workflow execution has timed out
   */
  hasExecutionTimedOut: ({ context }: { context: WorkflowContext }) => {
    const timeout = context.options?.timeout || 3600000; // 1 hour default
    const startTime = context.metadata?.startTime || Date.now();

    return (Date.now() - startTime) > timeout;
  },

  /**
   * Check if workflow should auto-pause on errors
   */
  shouldAutoPauseOnError: ({ context }: { context: WorkflowContext }) => {
    return (context.options as any)?.autoPauseOnError === true;
  },

  /**
   * Check if workflow has reached maximum concurrent nodes
   */
  hasReachedMaxConcurrency: ({ context }: { context: WorkflowContext }) => {
    const maxConcurrent = (context.options as any)?.maxConcurrentNodes || context.options?.maxConcurrency || 10;
    const runningNodes = context.runningNodes?.length || 0;

    return runningNodes >= maxConcurrent;
  },

  /**
   * Check if workflow should create checkpoint
   */
  shouldCreateCheckpoint: ({ context }: { context: WorkflowContext }) => {
    const checkpointInterval = (context.options as any)?.checkpointInterval || 300000; // 5 minutes
    const lastCheckpoint = (context.metadata as any)?.lastCheckpointTime || 0;

    return (Date.now() - lastCheckpoint) > checkpointInterval;
  },
};

/**
 * Node execution machine guards
 */
export const nodeExecutionGuards = {
  /**
   * Check if node validation was successful
   */
  isValidationSuccessful: ({ event }: { event: any }) => {
    return Boolean(event?.output && event.output.isValid);
  },

  /**
   * Check if node execution was successful
   */
  isExecutionSuccessful: ({ event }: { event: any }) => {
    return Boolean(event?.output && event.output.success);
  },

  /**
   * Check if node execution should be retried
   */
  shouldRetryExecution: ({ context, event }: { context: any; event: any }) => {
    const result = event?.output as DetailedNodeExecutionResult;
    if (!result) return false;

    return Boolean(
      !result.success &&
      result.shouldRetry !== false &&
      (context?.retryCount || 0) < (context?.maxRetries || 3)
    );
  },

  /**
   * Check if node should retry on timeout
   */
  shouldRetryOnTimeout: ({ context }: { context: any }) => {
    return (context?.retryCount || 0) < (context?.maxRetries || 3);
  },

  /**
   * Check if node can be retried
   */
  canRetry: ({ context }: { context: any }) => {
    return (context?.retryCount || 0) < (context?.maxRetries || 3);
  },

  /**
   * Check if node execution has specific retry conditions
   */
  hasRetryableError: ({ context }: { context: any }) => {
    if (!context?.error) {
      return false;
    }

    const retryableErrors = [
      'NETWORK_ERROR',
      'TIMEOUT',
      'RATE_LIMIT',
      'SERVICE_UNAVAILABLE',
      'TEMPORARY_FAILURE',
    ];

    const errorMessage = context.error.message || '';
    const errorCode = context.error.code || '';

    return retryableErrors.some(retryableError =>
      errorMessage.includes(retryableError) || errorCode === retryableError
    );
  },

  /**
   * Check if node is agent node
   */
  isAgentNode: ({ context }: { context: any }) => {
    return context?.nodeContext?.isAgentNode === true;
  },

  /**
   * Check if node has dependencies that are completed
   */
  dependenciesCompleted: ({ context }: { context: any }) => {
    const nodeContext = context?.nodeContext as NodeExecutionContext;

    // Use safe property access for dependencies
    const dependencies = (nodeContext?.node as any)?.dependencies;
    if (!dependencies || dependencies.length === 0) {
      return true;
    }

    // Check if all dependencies are completed
    // Note: We need to access dependencies from workflow context, not node context
    return dependencies.every((_depId: string) => {
      // This would need to be implemented with proper workflow context access
      return true; // Simplified for now
    });
  },

  /**
   * Check if node execution should be skipped
   */
  shouldSkipExecution: ({ context }: { context: any }) => {
    const nodeContext = context?.nodeContext as NodeExecutionContext;

    // Skip if node is disabled
    if ((nodeContext?.node as any)?.disabled === true) {
      return true;
    }

    // Skip if conditional execution fails
    if ((nodeContext?.node as any)?.condition) {
      // Evaluate condition logic here
      return false; // Simplified
    }

    return false;
  },

  /**
   * Check if node has required input data
   */
  hasRequiredInputData: ({ context }: { context: any }) => {
    const nodeContext = context?.nodeContext as NodeExecutionContext;

    const requiredInputs = (nodeContext?.node as any)?.requiredInputs;
    if (!requiredInputs) {
      return true;
    }

    // Check if all required inputs are present
    return requiredInputs.every((inputKey: string) => {
      return nodeContext?.inputData && nodeContext.inputData[inputKey] !== undefined;
    });
  },

  /**
   * Check if node execution should use cache
   */
  shouldUseCache: ({ context }: { context: any }) => {
    const nodeContext = context?.nodeContext as NodeExecutionContext;

    return (nodeContext?.node as any)?.cacheEnabled === true;
  },

  /**
   * Check if node has cached result
   */
  hasCachedResult: ({ context }: { context: any }) => {
    // Implementation would check cache store
    // For now, always return false as cache is not implemented
    return false; // Simplified - context would be used for cache lookup
  },
};

/**
 * Combined guards for easy import
 */
export const allGuards = {
  ...workflowGuards,
  ...nodeExecutionGuards,
};

/**
 * Guard utilities
 */
export const guardUtils = {
  /**
   * Create a custom guard with logging
   */
  createLoggedGuard: (name: string, guardFn: Function) => {
    return (params: any) => {
      const result = guardFn(params);
      console.log(`Guard [${name}]: ${result}`);
      return result;
    };
  },

  /**
   * Combine multiple guards with AND logic
   */
  combineGuardsAnd: (...guards: Function[]) => {
    return (params: any) => {
      return guards.every(guard => guard(params));
    };
  },

  /**
   * Combine multiple guards with OR logic
   */
  combineGuardsOr: (...guards: Function[]) => {
    return (params: any) => {
      return guards.some(guard => guard(params));
    };
  },

  /**
   * Negate a guard
   */
  not: (guard: Function) => {
    return (params: any) => {
      return !guard(params);
    };
  },
};
