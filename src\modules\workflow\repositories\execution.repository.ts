import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In, Between } from 'typeorm';
import { Execution } from '../entities/execution.entity';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';


/**
 * Repository cho Execution entity
 * Chứa các method CRUD và business logic cho workflow executions
 */
@Injectable()
export class ExecutionRepository {
  constructor(
    @InjectRepository(Execution)
    private readonly repository: Repository<Execution>,
  ) {}

  /**
   * Tạo execution mới
   */
  async create(executionData: Partial<Execution>): Promise<Execution> {
    const execution = this.repository.create({
      ...executionData,
      startedAt: Date.now(),
    });
    return await this.repository.save(execution);
  }

  /**
   * Tìm execution theo ID
   */
  async findById(id: string): Promise<Execution | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * <PERSON><PERSON><PERSON>hi<PERSON> executions theo IDs
   */
  async findByIds(ids: string[]): Promise<Execution[]> {
    return await this.repository.find({
      where: { id: In(ids) },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm tất cả executions của một workflow
   */
  async findByWorkflowId(workflowId: string): Promise<Execution[]> {
    return await this.repository.find({
      where: { workflowId },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions theo status
   */
  async findByStatus(status: ExecutionStatusEnum): Promise<Execution[]> {
    return await this.repository.find({
      where: { status },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions theo workflow và status
   */
  async findByWorkflowIdAndStatus(
    workflowId: string,
    status: ExecutionStatusEnum,
  ): Promise<Execution[]> {
    return await this.repository.find({
      where: { workflowId, status },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions đang chạy
   */
  async findRunningExecutions(): Promise<Execution[]> {
    return await this.repository.find({
      where: { status: ExecutionStatusEnum.RUNNING },
      order: { startedAt: 'ASC' },
    });
  }

  /**
   * Tìm executions thành công
   */
  async findSuccessfulExecutions(workflowId?: string): Promise<Execution[]> {
    const where: FindOptionsWhere<Execution> = { status: ExecutionStatusEnum.SUCCEEDED };
    if (workflowId) {
      where.workflowId = workflowId;
    }

    return await this.repository.find({
      where,
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions thất bại
   */
  async findFailedExecutions(workflowId?: string): Promise<Execution[]> {
    const where: FindOptionsWhere<Execution> = { status: ExecutionStatusEnum.FAILED };
    if (workflowId) {
      where.workflowId = workflowId;
    }

    return await this.repository.find({
      where,
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm execution gần nhất của workflow
   */
  async findLatestByWorkflowId(workflowId: string): Promise<Execution | null> {
    return await this.repository.findOne({
      where: { workflowId },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Cập nhật execution
   */
  async update(id: string, updateData: Partial<Execution>): Promise<Execution | null> {
    const updateResult = await this.repository.update(id, updateData);

    if ((updateResult.affected || 0) === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật status của execution
   */
  async updateStatus(id: string, status: ExecutionStatusEnum): Promise<Execution | null> {
    const updateData: Partial<Execution> = { status };

    // Cập nhật thời gian kết thúc nếu execution hoàn thành
    if (status === ExecutionStatusEnum.SUCCEEDED ||
        status === ExecutionStatusEnum.FAILED ||
        status === ExecutionStatusEnum.CANCELLED) {
      updateData.finishedAt = Date.now();
    }

    return await this.update(id, updateData);
  }

  /**
   * Cập nhật execution status (alias cho updateStatus để tương thích với machine-services)
   * Được sử dụng trong XState machine services
   */
  async updateExecutionStatus(executionId: string, status: 'completed' | 'failed' | 'running' | 'cancelled'): Promise<Execution | null> {
    // Map string status to ExecutionStatusEnum
    const statusMap: Record<string, ExecutionStatusEnum> = {
      'completed': ExecutionStatusEnum.SUCCEEDED,
      'failed': ExecutionStatusEnum.FAILED,
      'running': ExecutionStatusEnum.RUNNING,
      'cancelled': ExecutionStatusEnum.CANCELLED,
    };

    const executionStatus = statusMap[status];
    if (!executionStatus) {
      throw new Error(`Invalid execution status: ${status}. Valid values are: completed, failed, running, cancelled`);
    }

    const updateData: Partial<Execution> = {
      status: executionStatus,
    };

    // Cập nhật thời gian kết thúc nếu execution hoàn thành
    if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      updateData.finishedAt = Date.now();
    }

    // Log status change
    console.log(`Updating execution ${executionId} status to ${status} (${executionStatus})`);

    const result = await this.update(executionId, updateData);

    if (!result) {
      throw new Error(`Failed to update execution status for execution: ${executionId}`);
    }

    return result;
  }

  /**
   * Cập nhật execution status với metadata chi tiết từ workflow
   * Được sử dụng trong workflow machine cleanup service
   */
  async updateExecutionStatusWithMetadata(
    executionId: string,
    status: 'completed' | 'failed' | 'running' | 'cancelled',
    metadata?: {
      totalNodes?: number;
      completedNodes?: number;
      failedNodes?: number;
      errorMessage?: string;
    }
  ): Promise<Execution | null> {
    // Map string status to ExecutionStatusEnum
    const statusMap: Record<string, ExecutionStatusEnum> = {
      'completed': ExecutionStatusEnum.SUCCEEDED,
      'failed': ExecutionStatusEnum.FAILED,
      'running': ExecutionStatusEnum.RUNNING,
      'cancelled': ExecutionStatusEnum.CANCELLED,
    };

    const executionStatus = statusMap[status];
    if (!executionStatus) {
      throw new Error(`Invalid execution status: ${status}. Valid values are: completed, failed, running, cancelled`);
    }

    const updateData: Partial<Execution> = {
      status: executionStatus,
    };

    // Cập nhật error message nếu có
    if (metadata?.errorMessage) {
      updateData.errorMessage = metadata.errorMessage;
    }

    // Cập nhật thời gian kết thúc nếu execution hoàn thành
    if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      updateData.finishedAt = Date.now();
    }

    // Log status change với metadata
    console.log(`Updating execution ${executionId} status to ${status} (${executionStatus})`, {
      totalNodes: metadata?.totalNodes,
      completedNodes: metadata?.completedNodes,
      failedNodes: metadata?.failedNodes,
      hasErrorMessage: !!metadata?.errorMessage,
    });

    const result = await this.update(executionId, updateData);

    if (!result) {
      throw new Error(`Failed to update execution status for execution: ${executionId}`);
    }

    return result;
  }

  /**
   * Bulk update execution status cho nhiều executions
   * Hữu ích khi cần cancel hoặc fail nhiều executions cùng lúc
   */
  async bulkUpdateExecutionStatus(
    executionIds: string[],
    status: 'completed' | 'failed' | 'running' | 'cancelled'
  ): Promise<{ updated: number; failed: string[] }> {
    const statusMap: Record<string, ExecutionStatusEnum> = {
      'completed': ExecutionStatusEnum.SUCCEEDED,
      'failed': ExecutionStatusEnum.FAILED,
      'running': ExecutionStatusEnum.RUNNING,
      'cancelled': ExecutionStatusEnum.CANCELLED,
    };

    const executionStatus = statusMap[status];
    if (!executionStatus) {
      throw new Error(`Invalid execution status: ${status}. Valid values are: completed, failed, running, cancelled`);
    }

    const updateData: Partial<Execution> = {
      status: executionStatus,
    };

    // Cập nhật thời gian kết thúc nếu execution hoàn thành
    if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      updateData.finishedAt = Date.now();
    }

    try {
      const updateResult = await this.repository
        .createQueryBuilder()
        .update(Execution)
        .set(updateData)
        .where('id IN (:...ids)', { ids: executionIds })
        .execute();

      console.log(`Bulk updated ${updateResult.affected || 0} executions to status: ${status}`);

      return {
        updated: updateResult.affected || 0,
        failed: [],
      };
    } catch (error) {
      console.error(`Failed to bulk update executions:`, error);
      return {
        updated: 0,
        failed: executionIds,
      };
    }
  }

  /**
   * Tìm executions với advanced filtering
   * Hỗ trợ filtering theo multiple criteria
   */
  async findExecutionsWithAdvancedFilter(options: {
    workflowIds?: string[];
    statuses?: ('completed' | 'failed' | 'running' | 'cancelled')[];
    startDateFrom?: number;
    startDateTo?: number;
    finishedDateFrom?: number;
    finishedDateTo?: number;
    triggeredBy?: string;
    triggerType?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ executions: Execution[]; total: number }> {
    const queryBuilder = this.repository.createQueryBuilder('execution');

    // Filter by workflow IDs
    if (options.workflowIds && options.workflowIds.length > 0) {
      queryBuilder.andWhere('execution.workflowId IN (:...workflowIds)', {
        workflowIds: options.workflowIds,
      });
    }

    // Filter by statuses
    if (options.statuses && options.statuses.length > 0) {
      const statusEnums = options.statuses.map(status => {
        const statusMap: Record<string, ExecutionStatusEnum> = {
          'completed': ExecutionStatusEnum.SUCCEEDED,
          'failed': ExecutionStatusEnum.FAILED,
          'running': ExecutionStatusEnum.RUNNING,
          'cancelled': ExecutionStatusEnum.CANCELLED,
        };
        return statusMap[status];
      }).filter(Boolean);

      if (statusEnums.length > 0) {
        queryBuilder.andWhere('execution.status IN (:...statuses)', {
          statuses: statusEnums,
        });
      }
    }

    // Filter by start date range
    if (options.startDateFrom) {
      queryBuilder.andWhere('execution.startedAt >= :startDateFrom', {
        startDateFrom: options.startDateFrom,
      });
    }
    if (options.startDateTo) {
      queryBuilder.andWhere('execution.startedAt <= :startDateTo', {
        startDateTo: options.startDateTo,
      });
    }

    // Filter by finished date range
    if (options.finishedDateFrom) {
      queryBuilder.andWhere('execution.finishedAt >= :finishedDateFrom', {
        finishedDateFrom: options.finishedDateFrom,
      });
    }
    if (options.finishedDateTo) {
      queryBuilder.andWhere('execution.finishedAt <= :finishedDateTo', {
        finishedDateTo: options.finishedDateTo,
      });
    }

    // Filter by triggered by
    if (options.triggeredBy) {
      queryBuilder.andWhere('execution.triggeredBy = :triggeredBy', {
        triggeredBy: options.triggeredBy,
      });
    }

    // Filter by trigger type
    if (options.triggerType) {
      queryBuilder.andWhere('execution.triggerType = :triggerType', {
        triggerType: options.triggerType,
      });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    if (options.limit) {
      queryBuilder.limit(options.limit);
    }
    if (options.offset) {
      queryBuilder.offset(options.offset);
    }

    // Order by start date descending
    queryBuilder.orderBy('execution.startedAt', 'DESC');

    const executions = await queryBuilder.getMany();

    return {
      executions,
      total,
    };
  }

  /**
   * Cập nhật error message của execution
   */
  async updateErrorMessage(id: string, errorMessage: string): Promise<Execution | null> {
    return await this.update(id, {
      errorMessage,
      status: ExecutionStatusEnum.FAILED,
      finishedAt: Date.now(),
    });
  }

  /**
   * Bắt đầu execution
   */
  async startExecution(id: string): Promise<Execution | null> {
    return await this.update(id, {
      status: ExecutionStatusEnum.RUNNING,
      startedAt: Date.now(),
    });
  }

  /**
   * Hoàn thành execution thành công
   */
  async completeExecution(id: string): Promise<Execution | null> {
    const updateData: Partial<Execution> = {
      status: ExecutionStatusEnum.SUCCEEDED,
      finishedAt: Date.now(),
    };

    return await this.update(id, updateData);
  }

  /**
   * Thất bại execution
   */
  async failExecution(
    id: string,
    errorMessage?: string,
  ): Promise<Execution | null> {
    const updateData: Partial<Execution> = {
      status: ExecutionStatusEnum.FAILED,
      finishedAt: Date.now(),
    };

    if (errorMessage) {
      updateData.errorMessage = errorMessage;
    }

    return await this.update(id, updateData);
  }

  /**
   * Hủy execution
   */
  async cancelExecution(id: string): Promise<Execution | null> {
    return await this.update(id, {
      status: ExecutionStatusEnum.CANCELLED,
      finishedAt: Date.now(),
    });
  }

  /**
   * Xóa execution
   */
  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả executions của một workflow
   */
  async deleteByWorkflowId(workflowId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ workflowId });
    return deleteResult.affected || 0;
  }

  /**
   * Đếm số lượng executions
   */
  async count(where?: FindOptionsWhere<Execution>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm executions theo workflow
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return await this.repository.count({
      where: { workflowId },
    });
  }

  /**
   * Đếm executions theo status
   */
  async countByStatus(status: ExecutionStatusEnum): Promise<number> {
    return await this.repository.count({
      where: { status },
    });
  }

  /**
   * Đếm executions theo workflow và status
   */
  async countByWorkflowIdAndStatus(
    workflowId: string,
    status: ExecutionStatusEnum,
  ): Promise<number> {
    return await this.repository.count({
      where: { workflowId, status },
    });
  }

  /**
   * Tìm executions trong khoảng thời gian
   */
  async findByDateRange(startDate: number, endDate: number): Promise<Execution[]> {
    return await this.repository.find({
      where: {
        startedAt: Between(startDate, endDate),
      },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<Execution>,
  ): Promise<{ executions: Execution[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [executions, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { startedAt: 'DESC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      executions,
      total,
      totalPages,
    };
  }

  /**
   * Thống kê executions theo status
   */
  async getExecutionStats(workflowId?: string): Promise<{
    total: number;
    succeeded: number;
    failed: number;
    running: number;
    cancelled: number;
  }> {
    const baseWhere: FindOptionsWhere<Execution> = {};
    if (workflowId) {
      baseWhere.workflowId = workflowId;
    }

    const [total, succeeded, failed, running, cancelled] = await Promise.all([
      this.count(baseWhere),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.SUCCEEDED }),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.FAILED }),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.RUNNING }),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.CANCELLED }),
    ]);

    return {
      total,
      succeeded,
      failed,
      running,
      cancelled,
    };
  }

  /**
   * Tìm executions cũ để cleanup
   */
  async findOldExecutions(olderThanDays: number): Promise<Execution[]> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    return await this.repository
      .createQueryBuilder('execution')
      .where('execution.startedAt < :cutoffDate', { cutoffDate })
      .andWhere('execution.status != :runningStatus', {
        runningStatus: ExecutionStatusEnum.RUNNING
      })
      .orderBy('execution.startedAt', 'ASC')
      .getMany();
  }

  /**
   * Cleanup executions cũ
   */
  async cleanupOldExecutions(olderThanDays: number): Promise<number> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(Execution)
      .where('startedAt < :cutoffDate', { cutoffDate })
      .andWhere('status != :runningStatus', { 
        runningStatus: ExecutionStatusEnum.RUNNING 
      })
      .execute();

    return deleteResult.affected || 0;
  }
}
