import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { AdminWorkflowController } from './controllers/admin-workflow.controller';
import { UserWorkflowController } from './controllers/user-workflow.controller';
import {
  Connection,
  Execution,
  ExecutionNodeData,
  Node,
  NodeDefinition,
  WebhookRegistry,
  Workflow,
} from './entities';
import {
  ConnectionRepository,
  ExecutionNodeDataRepository,
  ExecutionRepository,
  NodeDefinitionRepository,
  NodeRepository,
  WebhookRegistryRepository,
  WorkflowRepository,
} from './repositories';
import { AdminWorkflowService } from './service/admin-workflow.service';
import { UserWorkflowService } from './service/user-workflow.service';
import { AdminWorkflowProcessor, UserWorkflowProcessor } from './processors';
import { WorkflowXStateService } from './services/xstate/services/workflow-xstate.service';
import { MachineIntegrationService } from './services/xstate/machines/machine-integration.service';
import { RedisPublisherService } from './services/redis-publisher.service';
import { DependencyResolverService } from './services/xstate/services/dependency-resolver.service';
import { WorkflowStateManagerService } from './services/xstate/services/workflow-state-manager.service';
import { NodeExecutorFactory, ExecutorRegistry } from './services/xstate/executors/node-executor.factory';
import { MachineServicesProvider } from './services/xstate/machines/machine-services';
import { AgentNodeDetectorService } from './services/xstate/services/agent-node-detector.service';
import { LangGraphIntegrationService } from './services/xstate/services/langgraph-integration.service';
import { QueueName } from '../../queue/queue-name.enum';

/**
 * Workflow Module
 * Chứa tất cả entities, repositories và services cho workflow system
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      ExecutionNodeData,
      NodeDefinition,
      WebhookRegistry,
    ]),
    BullModule.registerQueue({
      name: QueueName.WORKFLOW_EXECUTION,
    }),
  ],
  controllers: [
    UserWorkflowController,
    AdminWorkflowController,
  ],
  providers: [
    WorkflowRepository,
    NodeRepository,
    ConnectionRepository,
    ExecutionRepository,
    ExecutionNodeDataRepository,
    NodeDefinitionRepository,
    WebhookRegistryRepository,
    UserWorkflowService,
    AdminWorkflowService,
    AdminWorkflowProcessor,
    UserWorkflowProcessor,
    // XState Services
    DependencyResolverService,
    WorkflowStateManagerService,
    ExecutorRegistry,
    NodeExecutorFactory,
    AgentNodeDetectorService,
    LangGraphIntegrationService,
    MachineServicesProvider,
    WorkflowXStateService,
    MachineIntegrationService,
    // Redis Publisher
    RedisPublisherService,
  ],
  exports: [
    TypeOrmModule,
    WorkflowRepository,
    NodeRepository,
    ConnectionRepository,
    ExecutionRepository,
    ExecutionNodeDataRepository,
    NodeDefinitionRepository,
    WebhookRegistryRepository,
  ],
})
export class WorkflowModule { }