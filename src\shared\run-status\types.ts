/**
 * Run Status Type Definitions
 * 
 * Unified run status tracking system for AI processor execution lifecycle.
 * Replaces fragmented ActiveRunService + CancellationService approach.
 */

/**
 * Enumeration of possible run status states
 */
export enum RunStatusType {
  /** Run is currently being processed */
  ACTIVE = 'active',
  
  /** Run has completed successfully */
  COMPLETED = 'completed',
  
  /** Run was cancelled before completion */
  CANCELLED = 'cancelled',
  
  /** Run failed due to an error */
  FAILED = 'failed'
}

/**
 * Enumeration of possible cancellation reasons
 */
export enum CancelReason {
  /** User manually aborted the run */
  USER_ABORT = 'user_abort',
  
  /** New message interrupted the current run */
  MESSAGE_INTERRUPT = 'message_interrupt',
  
  /** Run timed out */
  TIMEOUT = 'timeout'
}

/**
 * Metadata associated with a run status
 */
export interface RunStatusMetadata {
  /** Timestamp when the run was created */
  createdAt: string;
  
  /** Timestamp when the run was last updated */
  updatedAt: string;
  
  /** Thread ID associated with this run */
  threadId: string;
  
  /** Unique run ID */
  runId: string;
  
  /** Reason for cancellation (only present if status is CANCELLED) */
  cancelReason?: CancelReason;
  
  /** Error message (only present if status is FAILED) */
  errorMessage?: string;
  
  /** Error stack trace (only present if status is FAILED) */
  errorStack?: string;
  
  /** Additional context data */
  context?: Record<string, any>;
}

/**
 * Complete run status object stored in Redis
 */
export interface RunStatus {
  /** Current status of the run */
  status: RunStatusType;
  
  /** Associated metadata */
  metadata: RunStatusMetadata;
}
