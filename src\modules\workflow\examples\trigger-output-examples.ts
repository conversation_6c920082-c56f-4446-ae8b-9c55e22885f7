/**
 * V<PERSON> dụ về format inputData cho UserNodeExecuteDto
 * Chỉ chứa outputs của trigger nodes đã xử lý ở BE App
 */

import { UserNodeExecuteDto } from '../dto/node-execute.dto';

/**
 * Ví dụ 1: Webhook trigger output
 * BE App đã xử lý webhook node và tạo output
 */
export const webhookTriggerExample: UserNodeExecuteDto = {
  userId: 123,
  workflowId: "workflow-456",
  nodeId: null, // null vì execute toàn bộ workflow
  type: "execute",
  inputData: {
    // Array chứa outputs của trigger nodes
    triggerNodeOutputs: [
      {
        nodeId: "webhook_node_1",
        outputData: {
          // Output từ webhook node
          webhookData: {
            method: "POST",
            headers: {
              "content-type": "application/json",
              "user-agent": "GitHub-Hookshot/abc123"
            },
            body: {
              action: "push",
              repository: {
                name: "my-repo",
                full_name: "user/my-repo"
              },
              commits: [
                {
                  id: "abc123",
                  message: "Update README",
                  author: {
                    name: "<PERSON>",
                    email: "<EMAIL>"
                  }
                }
              ]
            },
            query: {},
            timestamp: 1703123456789
          },
          // Processed data từ webhook
          extractedInfo: {
            repoName: "my-repo",
            commitCount: 1,
            lastCommitMessage: "Update README",
            author: "John Doe"
          }
        },
        success: true,
        processedAt: 1703123456789
      }
    ]
  }
};

/**
 * Ví dụ 2: Schedule trigger output
 * BE App đã xử lý schedule node và tạo output
 */
export const scheduleTriggerExample: UserNodeExecuteDto = {
  userId: 123,
  workflowId: "workflow-789",
  nodeId: null,
  type: "execute",
  inputData: {
    triggerNodeOutputs: [
      {
        nodeId: "schedule_node_1",
        outputData: {
          // Output từ schedule node
          scheduleInfo: {
            cronExpression: "0 9 * * 1-5", // 9 AM weekdays
            scheduledTime: "2024-01-15T09:00:00Z",
            actualTime: "2024-01-15T09:00:02Z",
            executionCount: 42
          },
          // Context data cho schedule
          contextData: {
            weekday: "Monday",
            isBusinessDay: true,
            timezone: "UTC",
            previousRun: "2024-01-12T09:00:00Z"
          }
        },
        success: true,
        processedAt: 1705305602000
      }
    ]
  }
};

/**
 * Ví dụ 3: Multiple trigger nodes
 * Workflow có nhiều trigger nodes (webhook + schedule)
 */
export const multipleTriggerExample: UserNodeExecuteDto = {
  userId: 123,
  workflowId: "workflow-multi",
  nodeId: null,
  type: "execute",
  inputData: {
    triggerNodeOutputs: [
      {
        nodeId: "webhook_node_1",
        outputData: {
          apiData: {
            endpoint: "/api/users",
            method: "POST",
            payload: { name: "John", email: "<EMAIL>" }
          }
        },
        success: true,
        processedAt: 1703123456789
      },
      {
        nodeId: "schedule_node_1", 
        outputData: {
          timeContext: {
            hour: 9,
            dayOfWeek: 1,
            isBusinessHour: true
          }
        },
        success: true,
        processedAt: 1703123456790
      }
    ]
  }
};

/**
 * Ví dụ 4: Alternative format - nodeOutputs object
 * Thay vì array, có thể dùng object với nodeId làm key
 */
export const alternativeFormatExample: UserNodeExecuteDto = {
  userId: 123,
  workflowId: "workflow-alt",
  nodeId: null,
  type: "execute",
  inputData: {
    // Alternative format: object với nodeId làm key
    nodeOutputs: {
      "webhook_node_1": {
        requestData: {
          url: "https://api.example.com/webhook",
          method: "POST",
          headers: { "authorization": "Bearer token123" }
        },
        responseData: {
          status: 200,
          data: { success: true, id: "abc123" }
        }
      },
      "schedule_node_2": {
        executionTime: "2024-01-15T09:00:00Z",
        batchSize: 100,
        processedRecords: 1250
      }
    }
  }
};

/**
 * Ví dụ 5: Single node execution với context
 * Execute một node cụ thể với outputs từ previous nodes
 */
export const singleNodeWithContextExample: UserNodeExecuteDto = {
  userId: 123,
  workflowId: "workflow-single",
  nodeId: "ai_agent_node_1", // Execute node cụ thể
  type: "execute",
  inputData: {
    // Context data cho node này
    contextData: {
      userInput: "Analyze the webhook data",
      previousResults: {
        webhookData: {
          repository: "user/my-repo",
          commits: 3,
          author: "John Doe"
        }
      }
    },
    // Outputs từ previous nodes
    triggerNodeOutputs: [
      {
        nodeId: "webhook_node_1",
        outputData: {
          repoInfo: {
            name: "my-repo",
            commits: [
              { message: "Fix bug", author: "John" },
              { message: "Add feature", author: "Jane" },
              { message: "Update docs", author: "Bob" }
            ]
          }
        }
      }
    ]
  }
};

/**
 * Ví dụ 6: Test mode với mock data
 */
export const testModeExample: UserNodeExecuteDto = {
  userId: 123,
  workflowId: "workflow-test",
  nodeId: "ai_agent_node_1",
  type: "test", // Test mode
  inputData: {
    // Mock trigger outputs cho testing
    triggerNodeOutputs: [
      {
        nodeId: "webhook_node_1",
        outputData: {
          mockWebhookData: {
            event: "test_event",
            data: { test: true, timestamp: Date.now() }
          }
        },
        success: true,
        processedAt: Date.now()
      }
    ],
    // Test context
    contextData: {
      testMode: true,
      mockData: true,
      testScenario: "webhook_processing"
    }
  }
};

/**
 * Helper function để tạo trigger output
 */
export function createTriggerOutput(
  nodeId: string,
  outputData: Record<string, any>,
  success: boolean = true
) {
  return {
    nodeId,
    outputData,
    success,
    processedAt: Date.now()
  };
}

/**
 * Helper function để tạo UserNodeExecuteDto cho workflow execution
 */
export function createWorkflowExecutionDto(
  userId: number,
  workflowId: string,
  triggerOutputs: Array<{ nodeId: string; outputData: any; success?: boolean }>
): UserNodeExecuteDto {
  return {
    userId,
    workflowId,
    nodeId: null,
    type: "execute",
    inputData: {
      triggerNodeOutputs: triggerOutputs.map(output => 
        createTriggerOutput(output.nodeId, output.outputData, output.success)
      )
    }
  };
}

/**
 * Helper function để tạo UserNodeExecuteDto cho single node execution
 */
export function createNodeExecutionDto(
  userId: number,
  workflowId: string,
  nodeId: string,
  contextData: Record<string, any>,
  triggerOutputs?: Array<{ nodeId: string; outputData: any }>
): UserNodeExecuteDto {
  return {
    userId,
    workflowId,
    nodeId,
    type: "execute",
    inputData: {
      contextData,
      triggerNodeOutputs: triggerOutputs?.map(output => 
        createTriggerOutput(output.nodeId, output.outputData)
      ) || []
    }
  };
}
