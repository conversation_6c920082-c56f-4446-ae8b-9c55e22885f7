/**
 * Interface cho job đồng bộ người dùng Zalo vào audience
 */
export interface ZaloAudienceSyncJobData {
  /**
   * ID của người dùng thực hiện đồng bộ
   */
  userId: number;

  /**
   * ID của Zalo Official Account
   */
  oaId: string;

  /**
   * Dữ liệu cấu hình đồng bộ
   */
  syncDto: any; // Tạm thời dùng any, sẽ thay bằng SyncZaloUsersToAudienceDto sau

  /**
   * ID để tracking job
   */
  syncId: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Interface cho job data của Zalo Video Tracking
 */
export interface ZaloVideoTrackingJobData {
  /**
   * Token của video upload từ Zalo API
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu video
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của Official Account (để tương thích với hệ thống cũ)
   */
  oaId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;
}

/**
 * Dữ liệu job thực thi workflow
 */
export interface WorkflowExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của user thực thi
   */
  userId: number;

  /**
   * ID của workflow
   */
  workflowId: string;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  }
}
/**
* ID của user thực thi
* Interface cho job data đồng bộ tin nhắn Zalo
*/
export interface ZaloMessageSyncJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * ID của Integration Zalo OA
  */
  integrationId: string;

  /**
   * Số lượng tin nhắn tối đa cần đồng bộ
   */
  limit: number;

  /**
   * Offset để phân trang
   */
  offset: number;

  /**
   * Chỉ đồng bộ tin nhắn từ những user-audience có zaloSocialId
   */
  onlyExistingAudience: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job data của Zalo Article Scheduler
 */
export interface ZaloArticleSchedulerJobData {
  /**
   * ID của bài viết trong database
   */
  articleId: string;

  /**
   * ID của user sở hữu bài viết
   */
  userId: number;

  /**
   * ID của Zalo Integration
   */
  integrationId: string;

  /**
   * Token của bài viết từ Zalo API (đã tạo với status hide)
   */
  articleToken: string;

  /**
   * Thời gian lên lịch xuất bản (Unix timestamp)
   */
  scheduledTime: number;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Metadata bổ sung (optional)
   */
  metadata?: {
    title?: string;
    description?: string;
    [key: string]: any;
  };
}

/**
 * Dữ liệu job thực thi node đơn lẻ
 */
export interface WorkflowNodeExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của user thực thi
   */
  userId: number;

  /**
   * ID của node cần thực thi
   */
  nodeId: string;

  /**
   * Loại node
   */
  nodeType: string;

  /**
   * Cấu hình node
   */
  nodeConfig: Record<string, any>;

  /**
   * Dữ liệu đầu vào cho node
   */
  inputData: Record<string, any>;

  /**
   * Context từ các node trước đó
   */
  executionContext: Record<string, any>;

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    skipValidation?: boolean;
  };
}

/**
 * Interface cho job data của Zalo Article Tracking
 */
export interface ZaloArticleTrackingJobData {
  /**
   * Token của bài viết từ Zalo API response
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu bài viết
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của bài viết trong database local (optional)
   */
  localArticleId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;

  /**
   * Metadata bổ sung (optional)
   */
  metadata?: {
    title?: string;
    type?: 'normal' | 'video';
    [key: string]: any;
  };
}
