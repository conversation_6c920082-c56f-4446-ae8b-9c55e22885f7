/**
 * Interface cho workflow trigger data từ BE App
 */

/**
 * Base trigger data interface
 */
export interface IWorkflowTriggerData {
  /** ID của workflow cần execute */
  workflowId: string;
  
  /** ID của user trigger workflow */
  userId: number;
  
  /** Loại trigger */
  triggerType: 'manual' | 'webhook' | 'schedule' | 'api';
  
  /** Timestamp khi trigger được tạo */
  triggeredAt: number;
  
  /** Metadata bổ sung */
  metadata?: Record<string, any>;
}

/**
 * Manual trigger data (từ UI)
 */
export interface IManualTriggerData extends IWorkflowTriggerData {
  triggerType: 'manual';
  
  /** ID của node cụ thể nếu chỉ execute 1 node */
  nodeId?: string;
  
  /** Input data cho node/workflow */
  inputData?: Record<string, any>;
}

/**
 * Webhook trigger data
 */
export interface IWebhookTriggerData extends IWorkflowTriggerData {
  triggerType: 'webhook';
  
  /** ID của webhook đã trigger */
  webhookId: string;
  
  /** HTTP method của webhook request */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  
  /** Headers từ webhook request */
  headers: Record<string, string>;
  
  /** Query parameters */
  query: Record<string, any>;
  
  /** Body data từ webhook */
  body: any;
  
  /** IP address của requester */
  sourceIp?: string;
  
  /** User agent */
  userAgent?: string;
}

/**
 * Schedule trigger data
 */
export interface IScheduleTriggerData extends IWorkflowTriggerData {
  triggerType: 'schedule';
  
  /** ID của schedule đã trigger */
  scheduleId: string;
  
  /** Cron expression */
  cronExpression: string;
  
  /** Scheduled time (có thể khác với triggeredAt do delay) */
  scheduledAt: number;
  
  /** Execution count của schedule này */
  executionCount: number;
  
  /** Context data cho schedule */
  scheduleContext?: Record<string, any>;
}

/**
 * API trigger data (từ external systems)
 */
export interface IApiTriggerData extends IWorkflowTriggerData {
  triggerType: 'api';
  
  /** API key hoặc token được sử dụng */
  apiKey?: string;
  
  /** External system identifier */
  externalSystemId?: string;
  
  /** Request data từ external API */
  requestData: any;
  
  /** Correlation ID để track request */
  correlationId?: string;
}

/**
 * Union type cho tất cả trigger data types
 */
export type WorkflowTriggerData = 
  | IManualTriggerData 
  | IWebhookTriggerData 
  | IScheduleTriggerData 
  | IApiTriggerData;

/**
 * Interface cho trigger node output data
 * Đây là data mà trigger nodes (webhook, schedule) sẽ output
 * để truyền cho processing nodes
 */
export interface ITriggerNodeOutput {
  /** ID của trigger node */
  nodeId: string;
  
  /** Loại trigger node */
  nodeType: 'webhook' | 'schedule' | 'manual';
  
  /** Raw trigger data */
  triggerData: WorkflowTriggerData;
  
  /** Processed output data cho downstream nodes */
  outputData: Record<string, any>;
  
  /** Timestamp khi trigger node được processed */
  processedAt: number;
  
  /** Success status */
  success: boolean;
  
  /** Error nếu có */
  error?: string;
}

/**
 * Interface cho workflow execution request từ BE App
 */
export interface IWorkflowExecutionRequest {
  /** Workflow trigger data */
  triggerData: WorkflowTriggerData;
  
  /** Execution options */
  options?: {
    /** Timeout cho toàn bộ workflow (ms) */
    timeout?: number;
    
    /** Max concurrent nodes */
    maxConcurrency?: number;
    
    /** Retry on failure */
    retryOnFailure?: boolean;
    
    /** Max retries */
    maxRetries?: number;
    
    /** Priority */
    priority?: 'low' | 'normal' | 'high' | 'critical';
  };
  
  /** Pre-computed trigger node outputs (nếu có) */
  triggerNodeOutputs?: ITriggerNodeOutput[];
}

/**
 * Helper functions
 */
export function isTriggerNode(nodeType: string): boolean {
  return ['webhook', 'schedule', 'manual-trigger'].includes(nodeType);
}

export function isProcessingNode(nodeType: string): boolean {
  return !isTriggerNode(nodeType);
}

export function createTriggerNodeOutput(
  nodeId: string,
  nodeType: 'webhook' | 'schedule' | 'manual',
  triggerData: WorkflowTriggerData,
  outputData: Record<string, any>
): ITriggerNodeOutput {
  return {
    nodeId,
    nodeType,
    triggerData,
    outputData,
    processedAt: Date.now(),
    success: true
  };
}
