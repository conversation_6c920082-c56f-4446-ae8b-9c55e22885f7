import { MachineServicesProvider } from './machine-services';
import { nodeExecutionMachine } from './node-execution.machine';
import { workflowMachine } from './workflow.machine';
import { allGuards } from './guards';
import { allActions } from './actions';

// XState Machines
export {
  workflowMachine,
  type WorkflowState,
  type WorkflowMachineEvent
} from './workflow.machine';

export {
  nodeExecutionMachine,
  type NodeExecutionState,
  type NodeExecutionMachineEvent
} from './node-execution.machine';

// Machine Services
export { MachineServicesProvider } from './machine-services';

// Machine Integration Service
export { MachineIntegrationService } from './machine-integration.service';

// Guards and Actions
export {
  workflowGuards,
  nodeExecutionGuards,
  allGuards,
  guardUtils
} from './guards';

export {
  workflowActions,
  nodeExecutionActions,
  allActions,
  actionUtils
} from './actions';

// Machine Configuration Helpers
export const createWorkflowMachineWithServices = (servicesProvider: MachineServicesProvider) => {
  return workflowMachine.provide({
    actors: servicesProvider.getMachineServices(),
  });
};

export const createNodeExecutionMachineWithServices = (servicesProvider: MachineServicesProvider): any => {
  return nodeExecutionMachine.provide({
    actors: servicesProvider.getMachineServices(),
  });
};

// Complete machine setup with all configurations
export const createCompleteWorkflowMachine = (servicesProvider: MachineServicesProvider) => {
  return workflowMachine.provide({
    actors: servicesProvider.getMachineServices(),
    guards: allGuards as any,
    actions: allActions as any,
    delays: {
      CHECKPOINT_INTERVAL: ({ context }: any) => (context.options as any)?.checkpointInterval || context.options?.timeout || 300000,
      TIMEOUT_DELAY: ({ context }: any) => context.options?.timeout || 3600000,
      RETRY_DELAY: ({ context }: any) => {
        const retryCount = context.metadata?.retryCount || 0;
        return Math.min(1000 * Math.pow(2, retryCount), 30000);
      },
    },
  });
};

export const createCompleteNodeExecutionMachine = (servicesProvider: MachineServicesProvider): any => {
  return nodeExecutionMachine.provide({
    actors: servicesProvider.getMachineServices(),
    guards: allGuards as any,
    actions: allActions as any,
    delays: {
      RETRY_DELAY: ({ context }: any) => Math.min(1000 * Math.pow(2, context.retryCount || 0), 30000),
      // Remove EXECUTION_TIMEOUT as it's not defined in the machine
    },
  });
};
