import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { WebhookRegistry } from '../entities/webhook-registry.entity';

/**
 * Repository cho WebhookRegistry entity
 * Chứa các method CRUD và business logic cho webhook registry
 */
@Injectable()
export class WebhookRegistryRepository {
  constructor(
    @InjectRepository(WebhookRegistry)
    private readonly repository: Repository<WebhookRegistry>,
  ) {}

  /**
   * Tạo webhook registry mới
   */
  async create(webhookData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
    const webhook = this.repository.create(webhookData);
    return await this.repository.save(webhook);
  }

  /**
   * Tạo nhiều webhook registries cùng lúc
   */
  async createMany(webhooksData: Partial<WebhookRegistry>[]): Promise<WebhookRegistry[]> {
    const webhooks = webhooksData.map(webhookData =>
      this.repository.create(webhookData)
    );
    return await this.repository.save(webhooks);
  }

  /**
   * Tìm webhook registry theo ID
   */
  async findById(id: string): Promise<WebhookRegistry | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm webhook registry theo webhook name
   */
  async findByWebhookName(webhookName: string): Promise<WebhookRegistry | null> {
    return await this.repository.findOne({
      where: { webhookName },
    });
  }

  /**
   * Tìm tất cả webhook registries
   */
  async findAll(): Promise<WebhookRegistry[]> {
    return await this.repository.find({
      order: { webhookName: 'ASC' },
    });
  }

  /**
   * Tìm webhook registries theo node ID
   */
  async findByNodeId(nodeId: string): Promise<WebhookRegistry[]> {
    return await this.repository.find({
      where: { nodeId },
      order: { webhookName: 'ASC' },
    });
  }

  /**
   * Tìm webhook registries theo workflow ID
   */
  async findByWorkflowId(workflowId: string): Promise<WebhookRegistry[]> {
    return await this.repository.find({
      where: { workflowId },
      order: { webhookName: 'ASC' },
    });
  }

  /**
   * Tìm webhook registries theo webhook name pattern
   */
  async findByWebhookNamePattern(namePattern: string): Promise<WebhookRegistry[]> {
    return await this.repository
      .createQueryBuilder('webhook')
      .where('webhook.webhookName ILIKE :namePattern', { namePattern: `%${namePattern}%` })
      .orderBy('webhook.webhookName', 'ASC')
      .getMany();
  }

  /**
   * Cập nhật webhook registry
   */
  async update(id: string, updateData: Partial<WebhookRegistry>): Promise<WebhookRegistry | null> {
    const updateResult = await this.repository.update(id, updateData);

    if (updateResult.affected === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật webhook name
   */
  async updateWebhookName(id: string, webhookName: string): Promise<WebhookRegistry | null> {
    return await this.update(id, { webhookName });
  }

  /**
   * Cập nhật node ID
   */
  async updateNodeId(id: string, nodeId: string): Promise<WebhookRegistry | null> {
    return await this.update(id, { nodeId });
  }

  /**
   * Cập nhật workflow ID
   */
  async updateWorkflowId(id: string, workflowId: string): Promise<WebhookRegistry | null> {
    return await this.update(id, { workflowId });
  }

  /**
   * Xóa webhook registry
   */
  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa webhook registry theo webhook name
   */
  async deleteByWebhookName(webhookName: string): Promise<boolean> {
    const deleteResult = await this.repository.delete({ webhookName });
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả webhook registries của một node
   */
  async deleteByNodeId(nodeId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ nodeId });
    return deleteResult.affected || 0;
  }

  /**
   * Xóa tất cả webhook registries của một workflow
   */
  async deleteByWorkflowId(workflowId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ workflowId });
    return deleteResult.affected || 0;
  }

  /**
   * Đếm số lượng webhook registries
   */
  async count(where?: FindOptionsWhere<WebhookRegistry>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm webhook registries theo node ID
   */
  async countByNodeId(nodeId: string): Promise<number> {
    return await this.repository.count({
      where: { nodeId },
    });
  }

  /**
   * Đếm webhook registries theo workflow ID
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return await this.repository.count({
      where: { workflowId },
    });
  }

  /**
   * Kiểm tra xem webhook name đã tồn tại chưa
   */
  async exists(webhookName: string): Promise<boolean> {
    const count = await this.repository.count({
      where: { webhookName },
    });
    return count > 0;
  }

  /**
   * Tìm webhook registries với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<WebhookRegistry>,
  ): Promise<{ webhooks: WebhookRegistry[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;

    const [webhooks, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { webhookName: 'ASC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      webhooks,
      total,
      totalPages,
    };
  }

  /**
   * Tìm tất cả unique webhook names
   */
  async findAllWebhookNames(): Promise<string[]> {
    const result = await this.repository
      .createQueryBuilder('webhook')
      .select('DISTINCT webhook.webhookName', 'webhookName')
      .orderBy('webhook.webhookName', 'ASC')
      .getRawMany();

    return result.map(row => row.webhookName);
  }

  /**
   * Upsert webhook registry (update nếu tồn tại, create nếu không)
   */
  async upsert(webhookData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
    const existing = await this.findByWebhookName(webhookData.webhookName!);

    if (existing) {
      return await this.update(existing.id, webhookData) || existing;
    } else {
      return await this.create(webhookData);
    }
  }

  /**
   * Bulk upsert webhook registries
   */
  async bulkUpsert(webhooksData: Partial<WebhookRegistry>[]): Promise<WebhookRegistry[]> {
    const results: WebhookRegistry[] = [];
    
    for (const webhookData of webhooksData) {
      const result = await this.upsert(webhookData);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Search webhook registries
   */
  async search(searchTerm: string): Promise<WebhookRegistry[]> {
    return await this.repository
      .createQueryBuilder('webhook')
      .where('webhook.webhookName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('webhook.webhookName', 'ASC')
      .getMany();
  }

  /**
   * Validate webhook name format
   */
  validateWebhookName(name: string): boolean {
    // Webhook name should contain valid characters
    const nameRegex = /^[a-zA-Z0-9\-_]+$/;
    return nameRegex.test(name);
  }

  /**
   * Cleanup orphaned webhook registries (không có node tương ứng)
   */
  async cleanupOrphanedWebhooks(existingNodeNames: string[]): Promise<number> {
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(WebhookRegistry)
      .where('nodeName NOT IN (:...nodeNames)', { nodeNames: existingNodeNames })
      .execute();

    return deleteResult.affected || 0;
  }
}
