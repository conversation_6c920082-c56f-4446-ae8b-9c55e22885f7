import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { WebhookRegistry } from '../entities/webhook-registry.entity';

/**
 * Repository cho WebhookRegistry entity
 * Chứa các method CRUD và business logic cho webhook registry
 */
@Injectable()
export class WebhookRegistryRepository {
  constructor(
    @InjectRepository(WebhookRegistry)
    private readonly repository: Repository<WebhookRegistry>,
  ) {}

  /**
   * Tạo webhook registry mới
   */
  async create(webhookData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
    const webhook = this.repository.create({
      ...webhookData,
      pathLength: webhookData.webhookPath?.length || 0,
    });
    return await this.repository.save(webhook);
  }

  /**
   * Tạo nhiều webhook registries cùng lúc
   */
  async createMany(webhooksData: Partial<WebhookRegistry>[]): Promise<WebhookRegistry[]> {
    const webhooks = webhooksData.map(webhookData => 
      this.repository.create({
        ...webhookData,
        pathLength: webhookData.webhookPath?.length || 0,
      })
    );
    return await this.repository.save(webhooks);
  }

  /**
   * Tìm webhook registry theo ID
   */
  async findById(id: string): Promise<WebhookRegistry | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm webhook registry theo path và method
   */
  async findByPathAndMethod(webhookPath: string, method: string): Promise<WebhookRegistry | null> {
    return await this.repository.findOne({
      where: { webhookPath, method: method.toUpperCase() },
    });
  }

  /**
   * Tìm tất cả webhook registries
   */
  async findAll(): Promise<WebhookRegistry[]> {
    return await this.repository.find({
      order: { webhookPath: 'ASC', method: 'ASC' },
    });
  }

  /**
   * Tìm webhook registries theo method
   */
  async findByMethod(method: string): Promise<WebhookRegistry[]> {
    return await this.repository.find({
      where: { method: method.toUpperCase() },
      order: { webhookPath: 'ASC' },
    });
  }

  /**
   * Tìm webhook registries theo node name
   */
  async findByNodeName(nodeName: string): Promise<WebhookRegistry[]> {
    return await this.repository.find({
      where: { nodeName } as any,
      order: { webhookPath: 'ASC' },
    });
  }

  /**
   * Tìm webhook registries theo path pattern
   */
  async findByPathPattern(pathPattern: string): Promise<WebhookRegistry[]> {
    return await this.repository
      .createQueryBuilder('webhook')
      .where('webhook.webhookPath ILIKE :pathPattern', { pathPattern: `%${pathPattern}%` })
      .orderBy('webhook.webhookPath', 'ASC')
      .getMany();
  }

  /**
   * Tìm webhook registry phù hợp nhất cho một path
   * (sử dụng path length để tìm match chính xác nhất)
   */
  async findBestMatchForPath(path: string, method: string): Promise<WebhookRegistry | null> {
    // Tìm exact match trước
    const exactMatch = await this.findByPathAndMethod(path, method);
    if (exactMatch) {
      return exactMatch;
    }

    // Tìm pattern matches, ưu tiên path dài nhất (cụ thể nhất)
    const matches = await this.repository
      .createQueryBuilder('webhook')
      .where('webhook.method = :method', { method: method.toUpperCase() })
      .andWhere(':path LIKE webhook.webhookPath', { path })
      .orderBy('webhook.pathLength', 'DESC')
      .getMany();

    return matches.length > 0 ? matches[0] : null;
  }

  /**
   * Cập nhật webhook registry
   */
  async update(id: string, updateData: Partial<WebhookRegistry>): Promise<WebhookRegistry | null> {
    const updatePayload = { ...updateData };
    
    // Cập nhật pathLength nếu webhookPath thay đổi
    if (updateData.webhookPath) {
      updatePayload.pathLength = updateData.webhookPath.length;
    }

    const updateResult = await this.repository.update(id, updatePayload);

    if (updateResult.affected === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật webhook path
   */
  async updateWebhookPath(id: string, webhookPath: string): Promise<WebhookRegistry | null> {
    return await this.update(id, { webhookPath });
  }

  /**
   * Cập nhật method
   */
  async updateMethod(id: string, method: string): Promise<WebhookRegistry | null> {
    return await this.update(id, { method: method.toUpperCase() });
  }

  /**
   * Cập nhật node name
   */
  async updateNodeName(id: string, nodeName: string): Promise<WebhookRegistry | null> {
    return await this.update(id, { nodeName } as any);
  }

  /**
   * Xóa webhook registry
   */
  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa webhook registry theo path và method
   */
  async deleteByPathAndMethod(webhookPath: string, method: string): Promise<boolean> {
    const deleteResult = await this.repository.delete({
      webhookPath,
      method: method.toUpperCase(),
    });
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả webhook registries của một node
   */
  async deleteByNodeName(nodeName: string): Promise<number> {
    const deleteResult = await this.repository.delete({ nodeName } as any);
    return deleteResult.affected || 0;
  }

  /**
   * Đếm số lượng webhook registries
   */
  async count(where?: FindOptionsWhere<WebhookRegistry>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm webhook registries theo method
   */
  async countByMethod(method: string): Promise<number> {
    return await this.repository.count({
      where: { method: method.toUpperCase() },
    });
  }

  /**
   * Đếm webhook registries theo node name
   */
  async countByNodeName(nodeName: string): Promise<number> {
    return await this.repository.count({
      where: { nodeName } as any,
    });
  }

  /**
   * Kiểm tra xem webhook path và method đã tồn tại chưa
   */
  async exists(webhookPath: string, method: string): Promise<boolean> {
    const count = await this.repository.count({
      where: { webhookPath, method: method.toUpperCase() },
    });
    return count > 0;
  }

  /**
   * Tìm webhook registries với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<WebhookRegistry>,
  ): Promise<{ webhooks: WebhookRegistry[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [webhooks, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { webhookPath: 'ASC', method: 'ASC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      webhooks,
      total,
      totalPages,
    };
  }

  /**
   * Tìm tất cả unique methods
   */
  async findAllMethods(): Promise<string[]> {
    const result = await this.repository
      .createQueryBuilder('webhook')
      .select('DISTINCT webhook.method', 'method')
      .orderBy('webhook.method', 'ASC')
      .getRawMany();
    
    return result.map(row => row.method);
  }

  /**
   * Tìm tất cả unique node names
   */
  async findAllNodeNames(): Promise<string[]> {
    const result = await this.repository
      .createQueryBuilder('webhook')
      .select('DISTINCT webhook.nodeName', 'nodeName')
      .orderBy('webhook.nodeName', 'ASC')
      .getRawMany();
    
    return result.map(row => row.nodeName);
  }

  /**
   * Upsert webhook registry (update nếu tồn tại, create nếu không)
   */
  async upsert(webhookData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
    const existing = await this.findByPathAndMethod(
      webhookData.webhookPath!,
      webhookData.method!
    );

    if (existing) {
      return await this.update(existing.id, webhookData) || existing;
    } else {
      return await this.create(webhookData);
    }
  }

  /**
   * Bulk upsert webhook registries
   */
  async bulkUpsert(webhooksData: Partial<WebhookRegistry>[]): Promise<WebhookRegistry[]> {
    const results: WebhookRegistry[] = [];
    
    for (const webhookData of webhooksData) {
      const result = await this.upsert(webhookData);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Tìm webhook registries theo multiple methods
   */
  async findByMethods(methods: string[]): Promise<WebhookRegistry[]> {
    const upperMethods = methods.map(method => method.toUpperCase());
    
    return await this.repository
      .createQueryBuilder('webhook')
      .where('webhook.method IN (:...methods)', { methods: upperMethods })
      .orderBy('webhook.webhookPath', 'ASC')
      .getMany();
  }

  /**
   * Search webhook registries
   */
  async search(searchTerm: string): Promise<WebhookRegistry[]> {
    return await this.repository
      .createQueryBuilder('webhook')
      .where('webhook.webhookPath ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('webhook.nodeName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('webhook.webhookPath', 'ASC')
      .getMany();
  }

  /**
   * Validate webhook path format
   */
  validateWebhookPath(path: string): boolean {
    // Webhook path should start with / and contain valid URL characters
    const pathRegex = /^\/[a-zA-Z0-9\-_\/\*\?]*$/;
    return pathRegex.test(path);
  }

  /**
   * Validate HTTP method
   */
  validateMethod(method: string): boolean {
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    return validMethods.includes(method.toUpperCase());
  }

  /**
   * Cleanup orphaned webhook registries (không có node tương ứng)
   */
  async cleanupOrphanedWebhooks(existingNodeNames: string[]): Promise<number> {
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(WebhookRegistry)
      .where('nodeName NOT IN (:...nodeNames)', { nodeNames: existingNodeNames })
      .execute();

    return deleteResult.affected || 0;
  }
}
