Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8BF0) msys-2.0.dll+0x1FE8E
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210286019, 0007FFFF9BA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9CF0  000210068E24 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9FD0  00021006A225 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA15460000 ntdll.dll
7FFA14D80000 KERNEL32.DLL
7FFA127A0000 KERNELBASE.dll
7FFA15060000 USER32.dll
7FFA12CE0000 win32u.dll
7FFA149F0000 GDI32.dll
7FFA12BA0000 gdi32full.dll
7FFA126F0000 msvcp_win.dll
7FFA12D10000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA14C50000 advapi32.dll
7FFA14A70000 msvcrt.dll
7FFA14BA0000 sechost.dll
7FFA14F00000 RPCRT4.dll
7FFA11B80000 CRYPTBASE.DLL
7FFA12650000 bcryptPrimitives.dll
7FFA15020000 IMM32.DLL
