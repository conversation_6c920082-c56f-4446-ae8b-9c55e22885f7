import { Injectable, Logger } from '@nestjs/common';
import { createActor, ActorRefFrom } from 'xstate';
import { workflowMachine } from './workflow.machine';
import { nodeExecutionMachine } from './node-execution.machine';
import { MachineServicesProvider } from './machine-services';
import { allGuards } from './guards';
import { allActions } from './actions';
import { createEnhancedWorkflowActions, createEnhancedNodeActions } from './enhanced-actions';
import { RedisPublisherService } from '../../redis-publisher.service';
import { WorkflowContext, NodeExecutionContext } from '../types';

/**
 * Machine integration service - connects XState machines with NestJS services
 */
@Injectable()
export class MachineIntegrationService {
  private readonly logger = new Logger(MachineIntegrationService.name);
  private readonly activeWorkflows = new Map<string, ActorRefFrom<typeof workflowMachine>>();
  private readonly activeNodes = new Map<string, ActorRefFrom<typeof nodeExecutionMachine>>();

  constructor(
    private readonly machineServices: MachineServicesProvider,
    private readonly redisPublisher: RedisPublisherService,
  ) {}

  /**
   * Create and configure workflow machine with all services
   */
  createWorkflowMachine(context: WorkflowContext) {
    try {
      this.logger.debug(`Creating workflow machine for execution: ${context.executionId}`);

      // Configure machine with services, guards, and enhanced actions
      const enhancedActions = createEnhancedWorkflowActions(this.redisPublisher);
      const configuredMachine = workflowMachine.provide({
        actors: this.machineServices.getMachineServices(),
        guards: allGuards as any,
        actions: {
          ...allActions as any,
          ...enhancedActions, // Override with Redis-enabled actions
        },
        delays: {
          CHECKPOINT_INTERVAL: ({ context }: { context: WorkflowContext }) => {
            return context.options?.timeout || 300000; // 5 minutes - use timeout as fallback
          },
          TIMEOUT_DELAY: ({ context }: { context: WorkflowContext }) => {
            return context.options?.timeout || 3600000; // 1 hour
          },
          RETRY_DELAY: ({ context }: { context: WorkflowContext }) => {
            const retryCount = context.metadata?.retryCount || 0;
            return Math.min(1000 * Math.pow(2, retryCount), 30000); // Exponential backoff
          },
        },
      });

      // Create actor with initial context
      const workflowActor = createActor(configuredMachine, {
        input: context,
      });

      // Store actor reference
      this.activeWorkflows.set(context.executionId, workflowActor);

      // Setup event listeners
      this.setupWorkflowEventListeners(workflowActor, context);

      return workflowActor;

    } catch (error) {
      this.logger.error(`Failed to create workflow machine for ${context.executionId}:`, error);
      throw error;
    }
  }

  /**
   * Create and configure node execution machine
   */
  createNodeExecutionMachine(nodeContext: NodeExecutionContext, config?: any): any {
    try {
      this.logger.debug(`Creating node execution machine for node: ${nodeContext.node.id}`);

      // Configure machine with services, guards, and actions
      const configuredMachine = nodeExecutionMachine.provide({
        actors: this.machineServices.getMachineServices(),
        guards: allGuards as any,
        actions: allActions as any,
        delays: {
          RETRY_DELAY: ({ context }: { context: any }) => {
            // Exponential backoff: 1s, 2s, 4s, 8s...
            return Math.min(1000 * Math.pow(2, context.retryCount || 0), 30000);
          },
        },
      });

      // Create actor with proper input structure
      const nodeActor = createActor(configuredMachine, {
        input: {
          ...nodeContext,
          config: config || {},
        },
      });

      // Store actor reference
      const nodeKey = `${nodeContext.executionId}-${nodeContext.node.id}`;
      this.activeNodes.set(nodeKey, nodeActor);

      // Setup event listeners
      this.setupNodeEventListeners(nodeActor, nodeContext);

      return nodeActor;

    } catch (error) {
      this.logger.error(`Failed to create node execution machine for ${nodeContext.node.id}:`, error);
      throw error;
    }
  }

  /**
   * Start workflow execution
   */
  async startWorkflow(context: WorkflowContext) {
    try {
      this.logger.log(`Starting workflow execution: ${context.executionId}`);

      const workflowActor = this.createWorkflowMachine(context);

      // Start the machine
      workflowActor.start();

      // Send initial load event
      workflowActor.send({
        type: 'LOAD_WORKFLOW',
        workflowId: context.workflowId,
        executionId: context.executionId,
        triggerData: context.triggerData,
      } as any);

      return workflowActor;

    } catch (error) {
      this.logger.error(`Failed to start workflow ${context.executionId}:`, error);
      throw error;
    }
  }

  /**
   * Pause workflow execution
   */
  async pauseWorkflow(executionId: string, reason?: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (!workflowActor) {
        throw new Error(`Workflow not found: ${executionId}`);
      }

      this.logger.log(`Pausing workflow: ${executionId}`);
      workflowActor.send({
        type: 'PAUSE_EXECUTION',
        reason,
      } as any);

    } catch (error) {
      this.logger.error(`Failed to pause workflow ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Resume workflow execution
   */
  async resumeWorkflow(executionId: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (!workflowActor) {
        throw new Error(`Workflow not found: ${executionId}`);
      }

      this.logger.log(`Resuming workflow: ${executionId}`);
      workflowActor.send({
        type: 'RESUME_EXECUTION',
      } as any);

    } catch (error) {
      this.logger.error(`Failed to resume workflow ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(executionId: string, reason?: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (!workflowActor) {
        throw new Error(`Workflow not found: ${executionId}`);
      }

      this.logger.log(`Cancelling workflow: ${executionId}`);
      workflowActor.send({
        type: 'CANCEL_EXECUTION',
        reason,
      } as any);

    } catch (error) {
      this.logger.error(`Failed to cancel workflow ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Get workflow status
   */
  getWorkflowStatus(executionId: string) {
    const workflowActor = this.activeWorkflows.get(executionId);
    if (!workflowActor) {
      return null;
    }

    try {
      const snapshot = workflowActor.getSnapshot();
      return {
        executionId,
        state: snapshot.value,
        context: snapshot.context,
        status: this.mapStateToStatus(snapshot.value),
        canSend: snapshot.can,
        tags: snapshot.tags,
      };
    } catch (error) {
      this.logger.error(`Error getting workflow status for ${executionId}:`, error);
      return {
        executionId,
        state: 'error',
        context: null,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get all active workflows
   */
  getActiveWorkflows() {
    const workflows: any[] = [];

    for (const [executionId, actor] of this.activeWorkflows.entries()) {
      try {
        const snapshot = actor.getSnapshot();
        workflows.push({
          executionId,
          state: snapshot.value,
          status: this.mapStateToStatus(snapshot.value),
          startTime: snapshot.context?.metadata?.startTime,
          totalNodes: snapshot.context?.metadata?.totalNodes || 0,
          completedNodes: snapshot.context?.metadata?.completedNodes || 0,
          failedNodes: snapshot.context?.metadata?.failedNodes || 0,
        });
      } catch (error) {
        this.logger.error(`Error getting snapshot for workflow ${executionId}:`, error);
        workflows.push({
          executionId,
          state: 'error',
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return workflows;
  }

  /**
   * Cleanup completed workflow
   */
  cleanupWorkflow(executionId: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (workflowActor) {
        // Stop workflow actor safely
        try {
          if (typeof workflowActor.stop === 'function') {
            workflowActor.stop();
          }
        } catch (stopError) {
          this.logger.warn(`Error stopping workflow actor ${executionId}:`, stopError);
        }

        this.activeWorkflows.delete(executionId);

        // Cleanup associated node actors
        const nodeKeysToDelete: string[] = [];
        for (const [nodeKey, nodeActor] of this.activeNodes.entries()) {
          if (nodeKey.startsWith(executionId)) {
            try {
              if (nodeActor && typeof nodeActor.stop === 'function') {
                nodeActor.stop();
              }
            } catch (stopError) {
              this.logger.warn(`Error stopping node actor ${nodeKey}:`, stopError);
            }
            nodeKeysToDelete.push(nodeKey);
          }
        }

        // Remove node actors from map
        nodeKeysToDelete.forEach(key => this.activeNodes.delete(key));

        this.logger.debug(`Cleaned up workflow: ${executionId}`, {
          nodeActorsRemoved: nodeKeysToDelete.length,
        });
      } else {
        this.logger.debug(`Workflow ${executionId} not found in active workflows`);
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup workflow ${executionId}:`, error);
    }
  }

  /**
   * Setup workflow event listeners
   */
  private setupWorkflowEventListeners(
    workflowActor: ActorRefFrom<typeof workflowMachine>,
    context: WorkflowContext
  ) {
    // Listen for state changes
    workflowActor.subscribe({
      next: (snapshot) => {
        this.logger.debug(`Workflow ${context.executionId} state: ${JSON.stringify(snapshot.value)}`);

        // Handle final states
        if (snapshot.status === 'done') {
          this.handleWorkflowCompletion(context.executionId, snapshot);
        }
      },
      error: (error) => {
        this.logger.error(`Workflow ${context.executionId} error:`, error);
        this.handleWorkflowError(context.executionId, error);
      },
      complete: () => {
        this.logger.debug(`Workflow ${context.executionId} completed`);
      },
    });
  }

  /**
   * Setup node event listeners
   */
  private setupNodeEventListeners(
    nodeActor: ActorRefFrom<typeof nodeExecutionMachine>,
    nodeContext: NodeExecutionContext
  ) {
    // Listen for state changes
    nodeActor.subscribe({
      next: (snapshot) => {
        this.logger.debug(`Node ${nodeContext.node.id} state: ${JSON.stringify(snapshot.value)}`);

        // Handle final states
        if (snapshot.status === 'done') {
          this.handleNodeCompletion(nodeContext, snapshot);
        }
      },
      error: (error) => {
        this.logger.error(`Node ${nodeContext.node.id} error:`, error);
        this.handleNodeError(nodeContext, error);
      },
      complete: () => {
        this.logger.debug(`Node ${nodeContext.node.id} completed`);
      },
    });
  }

  /**
   * Handle workflow completion
   */
  private handleWorkflowCompletion(executionId: string, snapshot: any) {
    this.logger.log(`Workflow completed: ${executionId}`, {
      finalState: snapshot.value,
      context: snapshot.context,
    });

    // Emit completion event
    // Implementation would emit to event bus or notification system

    // Schedule cleanup
    setTimeout(() => {
      this.cleanupWorkflow(executionId);
    }, 5000); // Cleanup after 5 seconds
  }

  /**
   * Handle workflow error
   */
  private handleWorkflowError(executionId: string, error: any) {
    this.logger.error(`Workflow error: ${executionId}`, error);

    // Emit error event
    // Implementation would emit to event bus or notification system
  }

  /**
   * Handle node completion
   */
  private handleNodeCompletion(nodeContext: NodeExecutionContext, snapshot: any) {
    this.logger.debug(`Node completed: ${nodeContext.node.id}`, {
      finalState: snapshot.value,
      context: snapshot.context,
    });

    // Cleanup node actor
    const nodeKey = `${nodeContext.executionId}-${nodeContext.node.id}`;
    this.activeNodes.delete(nodeKey);
  }

  /**
   * Handle node error
   */
  private handleNodeError(nodeContext: NodeExecutionContext, error: any) {
    this.logger.error(`Node error: ${nodeContext.node.id}`, error);

    // Cleanup node actor
    const nodeKey = `${nodeContext.executionId}-${nodeContext.node.id}`;
    this.activeNodes.delete(nodeKey);
  }

  /**
   * Map machine state to human-readable status
   */
  private mapStateToStatus(state: any): string {
    if (typeof state === 'string') {
      return state;
    }

    if (typeof state === 'object' && state !== null) {
      // Handle parallel states
      if ('executing' in state) {
        return 'executing';
      }

      // Handle nested states
      if ('nodeExecution' in state) {
        return 'executing';
      }

      // Return first state key
      const keys = Object.keys(state);
      if (keys.length > 0) {
        return keys[0];
      }
    }

    return 'unknown';
  }

  /**
   * Get machine statistics
   */
  getMachineStatistics() {
    const workflowStates: string[] = [];
    let errorCount = 0;

    for (const actor of this.activeWorkflows.values()) {
      try {
        const snapshot = actor.getSnapshot();
        workflowStates.push(this.mapStateToStatus(snapshot.value));
      } catch (error) {
        errorCount++;
        workflowStates.push('error');
        this.logger.warn('Error getting workflow state for statistics:', error);
      }
    }

    return {
      activeWorkflows: this.activeWorkflows.size,
      activeNodes: this.activeNodes.size,
      workflowStates,
      errorCount,
      timestamp: Date.now(),
    };
  }

  /**
   * Force cleanup all machines (for shutdown)
   */
  async shutdown() {
    this.logger.log('Shutting down machine integration service', {
      activeWorkflows: this.activeWorkflows.size,
      activeNodes: this.activeNodes.size,
    });

    let workflowErrors = 0;
    let nodeErrors = 0;

    // Stop all workflow actors
    for (const [executionId, actor] of this.activeWorkflows.entries()) {
      try {
        if (actor && typeof actor.stop === 'function') {
          actor.stop();
        }
      } catch (error) {
        workflowErrors++;
        this.logger.error(`Error stopping workflow ${executionId}:`, error);
      }
    }

    // Stop all node actors
    for (const [nodeKey, actor] of this.activeNodes.entries()) {
      try {
        if (actor && typeof actor.stop === 'function') {
          actor.stop();
        }
      } catch (error) {
        nodeErrors++;
        this.logger.error(`Error stopping node ${nodeKey}:`, error);
      }
    }

    // Clear maps
    this.activeWorkflows.clear();
    this.activeNodes.clear();

    this.logger.log('Machine integration service shutdown complete', {
      workflowErrors,
      nodeErrors,
    });
  }
}
