import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { NodeDefinition } from '../entities/node-definition.entity';
import { NodeGroupEnum } from '../enums/node-group.enum';
import { ICredentialDefinition, INodeProperty } from '../interfaces/node-manager.interface';

/**
 * Repository cho NodeDefinition entity
 * Chứa các method CRUD và business logic cho node definitions
 */
@Injectable()
export class NodeDefinitionRepository {
  constructor(
    @InjectRepository(NodeDefinition)
    private readonly repository: Repository<NodeDefinition>,
  ) {}

  /**
   * Tạo node definition mới
   */
  async create(nodeDefData: Partial<NodeDefinition>): Promise<NodeDefinition> {
    const nodeDefinition = this.repository.create({
      ...nodeDefData,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return await this.repository.save(nodeDefinition);
  }

  /**
   * Tạo nhiều node definitions cùng lúc
   */
  async createMany(nodeDefsData: Partial<NodeDefinition>[]): Promise<NodeDefinition[]> {
    const nodeDefinitions = nodeDefsData.map(nodeDefData => 
      this.repository.create({
        ...nodeDefData,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      })
    );
    return await this.repository.save(nodeDefinitions);
  }

  /**
   * Tìm node definition theo ID
   */
  async findById(id: string): Promise<NodeDefinition | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm node definition theo type name và version
   */
  async findByTypeNameAndVersion(typeName: string, version: number): Promise<NodeDefinition | null> {
    return await this.repository.findOne({
      where: { typeName: typeName as any, version },
    });
  }

  /**
   * Tìm tất cả versions của một type name
   */
  async findVersionsByTypeName(typeName: string): Promise<NodeDefinition[]> {
    return await this.repository.find({
      where: { typeName: typeName as any },
      order: { version: 'DESC' },
    });
  }

  /**
   * Tìm version mới nhất của một type name
   */
  async findLatestVersionByTypeName(typeName: string): Promise<NodeDefinition | null> {
    return await this.repository.findOne({
      where: { typeName: typeName as any },
      order: { version: 'DESC' },
    });
  }

  /**
   * Tìm tất cả node definitions
   */
  async findAll(): Promise<NodeDefinition[]> {
    return await this.repository.find({
      order: { groupName: 'ASC', displayName: 'ASC' },
    });
  }

  /**
   * Tìm node definitions theo group
   */
  async findByGroup(groupName: NodeGroupEnum): Promise<NodeDefinition[]> {
    return await this.repository.find({
      where: { groupName },
      order: { displayName: 'ASC' },
    });
  }

  /**
   * Tìm node definitions theo tên hiển thị
   */
  async findByDisplayName(displayName: string): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.displayName ILIKE :displayName', { displayName: `%${displayName}%` })
      .orderBy('nodeDef.displayName', 'ASC')
      .getMany();
  }

  /**
   * Tìm node definitions theo description
   */
  async searchByDescription(searchTerm: string): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('nodeDef.displayName', 'ASC')
      .getMany();
  }

  /**
   * Tìm node definitions có credentials
   */
  async findWithCredentials(): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.credentials IS NOT NULL')
      .andWhere('jsonb_array_length(nodeDef.credentials) > 0')
      .orderBy('nodeDef.displayName', 'ASC')
      .getMany();
  }

  /**
   * Tìm node definitions theo credential type
   */
  async findByCredentialType(credentialType: string): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.credentials @> :credentialFilter', {
        credentialFilter: JSON.stringify([{ name: credentialType }])
      })
      .orderBy('nodeDef.displayName', 'ASC')
      .getMany();
  }

  /**
   * Cập nhật node definition
   */
  async update(id: string, updateData: Partial<NodeDefinition>): Promise<NodeDefinition | null> {
    const updateResult = await this.repository.update(id, {
      ...updateData,
      updatedAt: Date.now(),
    });

    if (updateResult.affected === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật properties
   */
  async updateProperties(id: string, properties: INodeProperty[]): Promise<NodeDefinition | null> {
    return await this.update(id, { properties });
  }

  /**
   * Cập nhật credentials
   */
  async updateCredentials(id: string, credentials: ICredentialDefinition[]): Promise<NodeDefinition | null> {
    return await this.update(id, { credentials });
  }

  /**
   * Xóa node definition
   */
  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả versions của một type name
   */
  async deleteByTypeName(typeName: string): Promise<number> {
    const deleteResult = await this.repository.delete({ typeName: typeName as any });
    return deleteResult.affected || 0;
  }

  /**
   * Đếm số lượng node definitions
   */
  async count(where?: FindOptionsWhere<NodeDefinition>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm node definitions theo group
   */
  async countByGroup(groupName: NodeGroupEnum): Promise<number> {
    return await this.repository.count({
      where: { groupName },
    });
  }

  /**
   * Đếm unique type names
   */
  async countUniqueTypeNames(): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('nodeDef')
      .select('COUNT(DISTINCT nodeDef.typeName)', 'count')
      .getRawOne();
    
    return parseInt(result.count) || 0;
  }

  /**
   * Tìm node definitions với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<NodeDefinition>,
  ): Promise<{ nodeDefinitions: NodeDefinition[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [nodeDefinitions, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { displayName: 'ASC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      nodeDefinitions,
      total,
      totalPages,
    };
  }

  /**
   * Tìm tất cả unique type names
   */
  async findAllTypeNames(): Promise<string[]> {
    const result = await this.repository
      .createQueryBuilder('nodeDef')
      .select('DISTINCT nodeDef.typeName', 'typeName')
      .orderBy('nodeDef.typeName', 'ASC')
      .getRawMany();
    
    return result.map(row => row.typeName);
  }

  /**
   * Tìm tất cả groups có node definitions
   */
  async findAllGroups(): Promise<NodeGroupEnum[]> {
    const result = await this.repository
      .createQueryBuilder('nodeDef')
      .select('DISTINCT nodeDef.groupName', 'groupName')
      .orderBy('nodeDef.groupName', 'ASC')
      .getRawMany();
    
    return result.map(row => row.groupName);
  }

  /**
   * Upsert node definition (update nếu tồn tại, create nếu không)
   */
  async upsert(nodeDefData: Partial<NodeDefinition>): Promise<NodeDefinition> {
    const existing = await this.findByTypeNameAndVersion(
      nodeDefData.typeName!,
      nodeDefData.version!
    );

    if (existing) {
      return await this.update(existing.id, nodeDefData) || existing;
    } else {
      return await this.create(nodeDefData);
    }
  }

  /**
   * Bulk upsert node definitions
   */
  async bulkUpsert(nodeDefsData: Partial<NodeDefinition>[]): Promise<NodeDefinition[]> {
    const results: NodeDefinition[] = [];
    
    for (const nodeDefData of nodeDefsData) {
      const result = await this.upsert(nodeDefData);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Tìm node definitions theo multiple groups
   */
  async findByGroups(groupNames: NodeGroupEnum[]): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.groupName IN (:...groupNames)', { groupNames })
      .orderBy('nodeDef.groupName', 'ASC')
      .addOrderBy('nodeDef.displayName', 'ASC')
      .getMany();
  }

  /**
   * Search node definitions (tìm kiếm trong tên và mô tả)
   */
  async search(searchTerm: string): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.displayName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('nodeDef.description ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('nodeDef.typeName ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('nodeDef.displayName', 'ASC')
      .getMany();
  }

  /**
   * Tìm node definitions được tạo trong khoảng thời gian
   */
  async findByDateRange(startDate: number, endDate: number): Promise<NodeDefinition[]> {
    return await this.repository
      .createQueryBuilder('nodeDef')
      .where('nodeDef.createdAt >= :startDate', { startDate })
      .andWhere('nodeDef.createdAt <= :endDate', { endDate })
      .orderBy('nodeDef.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Cleanup node definitions cũ (xóa versions cũ, giữ lại version mới nhất)
   */
  async cleanupOldVersions(): Promise<number> {
    // Tìm tất cả type names
    const typeNames = await this.findAllTypeNames();
    let deletedCount = 0;

    for (const typeName of typeNames) {
      const versions = await this.findVersionsByTypeName(typeName);
      
      // Giữ lại version mới nhất, xóa các versions cũ
      if (versions.length > 1) {
        const versionsToDelete = versions.slice(1); // Bỏ qua version đầu tiên (mới nhất)
        
        for (const version of versionsToDelete) {
          const deleted = await this.delete(version.id);
          if (deleted) {
            deletedCount++;
          }
        }
      }
    }

    return deletedCount;
  }
}
