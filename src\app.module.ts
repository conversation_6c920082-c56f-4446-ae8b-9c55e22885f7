import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig, env } from './config';
import { InfraModule } from './infra';
import { DataProcessModule } from './modules/data-process';
import { DatabaseModule } from './modules/database';
import { EmailSystemModule } from './modules/email-system/email-system.module';
import { IntegrationModule } from './modules/integration/integration.module';
import { EmailMarketingModule } from './modules/marketing/email/email-marketing.module';
import { MarketingModule } from './modules/marketing/marketing.module';
import { SmsMarketingModule } from './modules/marketing/sms/sms-marketing.module';
import { ZaloAudienceSyncModule } from './modules/marketing/zalo-audience-sync/zalo-audience-sync.module';
import { AgentsModule } from './modules/agents/agents.module';
import { ZaloVideoTrackingModule } from './modules/marketing/zalo-video-tracking/zalo-video-tracking.module';
import { ZaloZnsModule } from './modules/marketing/zalo/zalo-zns.module';
import { SmsSystemModule } from './modules/sms_system/sms-system.module';
import { WebhookModule } from './modules/webhook/webhook.module';
import { WorkflowModule } from './modules/workflow/workflow.module';
import { QueueModule } from './queue';
import { SharedModule } from './shared/shared.module';
import { ZaloConsultationSequenceModule, ZaloGroupMessageSequenceModule } from './modules/marketing/zalo';
import { ZaloArticleSchedulerModule } from './modules/marketing/zalo-article-scheduler/zalo-article-scheduler.module';
import { ZaloArticleTrackingModule } from './modules/marketing/zalo-article-tracking/zalo-article-tracking.module';

@Module({
  imports: [
    SharedModule,
    QueueModule,
    InfraModule,
    DatabaseModule,
    EmailSystemModule,
    EmailMarketingModule,
    SmsMarketingModule,
    SmsSystemModule,
    ZaloZnsModule,
    ZaloConsultationSequenceModule,
    ZaloGroupMessageSequenceModule,
    IntegrationModule,
    DataProcessModule,
    ZaloAudienceSyncModule,
    ZaloVideoTrackingModule,
    ZaloArticleSchedulerModule,
    ZaloArticleTrackingModule,
    MarketingModule,
    WebhookModule,
    WorkflowModule,
    TypeOrmModule.forRoot(databaseConfig),
    BullModule.forRoot({
      connection: {
        url: env.external.REDIS_URL,
      },
    }),
    AgentsModule,
  ],
})
export class AppModule { }
