import { UserWorkflowService } from './../service/user-workflow.service';
import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { NodeResponseDto, UserNodeExecuteDto } from '../dto/node-execute.dto';

@Controller()
export class UserWorkflowController {
    private readonly logger = new Logger(UserWorkflowController.name);

    constructor(private readonly userWorkflowService: UserWorkflowService) { }

    /**
     * Handle node execution - test hoặc execute một node cụ thể
     */
    @MessagePattern({ cmd: 'user_execute_node' })
    async handleExecuteNode(@Payload() data: UserNodeExecuteDto): Promise<NodeResponseDto> {
        this.logger.log(`Received node execution request:`, {
            userId: data.userId,
            workflowId: data.workflowId,
            nodeId: data.nodeId,
            type: data.type
        });

        try {
            // <PERSON><PERSON><PERSON> tra type để xác đ<PERSON>nh mode execution
            if (data.type === 'test') {
                // Test mode - chỉ test node mà không lưu vào database chính
                const result = await this.userWorkflowService.testNode(data);
                return { result };
            } else if (data.type === 'execute') {
                // Execute mode - thực thi node và lưu kết quả
                const result = await this.userWorkflowService.executeNode(data);
                return { result };
            } else {
                throw new Error(`Invalid execution type: ${data.type}. Must be 'test' or 'execute'`);
            }
        } catch (error) {
            this.logger.error(`Node execution failed:`, error);
            return {
                result: {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                }
            };
        }
    }

    /**
     * Handle workflow execution - chạy toàn bộ workflow
     */
    @MessagePattern({ cmd: 'user_execute_workflow' })
    async handleExecuteWorkflow(@Payload() data: UserNodeExecuteDto): Promise<NodeResponseDto> {
        this.logger.log(`Received workflow execution request:`, {
            userId: data.userId,
            workflowId: data.workflowId,
            type: data.type
        });

        try {
            // Kiểm tra type để xác định mode execution
            if (data.type === 'test') {
                // Test mode - chạy workflow test mà không lưu vào database chính
                const result = await this.userWorkflowService.testWorkflow(data);
                return { result };
            } else if (data.type === 'execute') {
                // Execute mode - thực thi workflow hoàn chỉnh
                const result = await this.userWorkflowService.executeWorkflow(data);
                return { result };
            } else {
                throw new Error(`Invalid execution type: ${data.type}. Must be 'test' or 'execute'`);
            }
        } catch (error) {
            this.logger.error(`Workflow execution failed:`, error);
            return {
                result: {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                }
            };
        }
    }
}