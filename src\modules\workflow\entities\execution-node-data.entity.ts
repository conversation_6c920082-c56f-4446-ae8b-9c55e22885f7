import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';
import { IExecutionData } from '../interfaces';

@Entity('execution_node_data')
@Index('idx_execution_node_data_execution_id', ['executionId'])
export class ExecutionNodeData {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'execution_id', type: 'uuid' })
  executionId: string;

  @Column({ name: 'node_id', type: 'uuid' })
  nodeId: string;

  @Column({ name: 'node_name', type: 'varchar', length: 255 })
  nodeName: string;

  @Column({ name: 'input_data', type: 'jsonb', nullable: true })
  inputData: IExecutionData;

  @Column({ name: 'output_data', type: 'jsonb', nullable: true })
  outputData: IExecutionData;

  @Column({
    name: 'executed_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  executedAt: number;
}
