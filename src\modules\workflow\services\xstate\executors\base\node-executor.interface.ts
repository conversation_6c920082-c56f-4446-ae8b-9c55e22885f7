import { 
  NodeExecutionContext, 
  DetailedNodeExecutionResult, 
  NodeExecutionConfig,
  NodeExecutionStatus,
  NodeExecutionPriority 
} from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * Interface contract cho tất cả node executors
 * Mỗi node type sẽ implement interface này
 */
export interface INodeExecutor {
  /**
   * Node group mà executor này xử lý
   */
  readonly nodeGroup: NodeGroupEnum;
  
  /**
   * Danh sách node types cụ thể mà executor này hỗ trợ
   */
  readonly supportedNodeTypes: string[];
  
  /**
   * Tên của executor (để logging và debugging)
   */
  readonly executorName: string;
  
  /**
   * Version của executor
   */
  readonly version: string;
  
  /**
   * Kiểm tra xem executor có thể xử lý node type này không
   * @param nodeType - Type của node cần kiểm tra
   * @returns true nếu có thể xử lý
   */
  canHandle(nodeType: string): boolean;
  
  /**
   * Validate input data trước khi thực thi
   * @param context - Node execution context
   * @returns Promise<ValidationResult>
   */
  validateInput(context: NodeExecutionContext): Promise<ValidationResult>;
  
  /**
   * Thực thi node với context đã cho
   * @param context - Node execution context
   * @param config - Execution configuration
   * @returns Promise<DetailedNodeExecutionResult>
   */
  execute(
    context: NodeExecutionContext, 
    config?: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult>;
  
  /**
   * Validate output data sau khi thực thi
   * @param outputData - Dữ liệu output cần validate
   * @param context - Node execution context
   * @returns Promise<ValidationResult>
   */
  validateOutput(outputData: any, context: NodeExecutionContext): Promise<ValidationResult>;
  
  /**
   * Cleanup resources sau khi thực thi (optional)
   * @param context - Node execution context
   */
  cleanup?(context: NodeExecutionContext): Promise<void>;
  
  /**
   * Estimate execution time (optional, để scheduling)
   * @param context - Node execution context
   * @returns Estimated time in milliseconds
   */
  estimateExecutionTime?(context: NodeExecutionContext): number;

  /**
   * Health check for executor (optional)
   * @returns Promise<boolean> - true if healthy
   */
  healthCheck?(): Promise<boolean>;

  /**
   * Initialize executor (optional)
   */
  initialize?(): Promise<void>;

  /**
   * Destroy executor and cleanup resources (optional)
   */
  destroy?(): Promise<void>;
  
  /**
   * Get default configuration cho node type này
   * @returns Default NodeExecutionConfig
   */
  getDefaultConfig(): NodeExecutionConfig;
  
  /**
   * Handle retry logic (optional, override default retry)
   * @param context - Node execution context
   * @param error - Error từ lần thực thi trước
   * @param retryCount - Số lần đã retry
   * @returns Promise<boolean> - true nếu nên retry
   */
  shouldRetry?(
    context: NodeExecutionContext, 
    error: Error, 
    retryCount: number
  ): Promise<boolean>;
}

/**
 * Kết quả validation
 */
export interface ValidationResult {
  /** Validation có thành công không */
  isValid: boolean;
  
  /** Danh sách lỗi nếu có */
  errors: ValidationError[];
  
  /** Danh sách warnings nếu có */
  warnings: ValidationWarning[];
  
  /** Có thể auto-fix không */
  canAutoFix: boolean;
  
  /** Dữ liệu đã được fix (nếu canAutoFix = true) */
  fixedData?: any;
}

/**
 * Validation error
 */
export interface ValidationError {
  /** Mã lỗi */
  code: string;
  
  /** Thông báo lỗi */
  message: string;
  
  /** Field bị lỗi */
  field?: string;
  
  /** Giá trị hiện tại */
  currentValue?: any;
  
  /** Giá trị mong đợi */
  expectedValue?: any;
  
  /** Severity của lỗi */
  severity: 'error' | 'warning' | 'info';
}

/**
 * Validation warning
 */
export interface ValidationWarning {
  /** Mã warning */
  code: string;
  
  /** Thông báo warning */
  message: string;
  
  /** Field có warning */
  field?: string;
  
  /** Suggestion để fix */
  suggestion?: string;
}

/**
 * Base execution context với core runtime properties
 */
export interface BaseExecutorContext extends NodeExecutionContext {
  /** Start time của execution */
  startTime: number;

  /** Current retry count */
  currentRetryCount: number;

  /** Execution status */
  status: NodeExecutionStatus;

  /** Priority của execution */
  priority: NodeExecutionPriority;

  /** Có đang trong retry mode không */
  isRetrying: boolean;

  /** Có đang trong debug mode không */
  isDebugMode: boolean;

  /** Logger instance */
  logger: any;

  /** Event emitter để gửi events */
  eventEmitter: any;

  /** Metrics collector */
  metricsCollector?: any;

  /** Cache service */
  cacheService?: any;

  /** HTTP client service */
  httpService?: any;

  /** Database service */
  databaseService?: any;

  /** File system service */
  fileService?: any;

  /** Encryption service */
  encryptionService?: any;

  /** Custom services */
  customServices?: Record<string, any>;
}

/**
 * Generic execution context với typed output support
 */
export type ExecutorContext<TOutput = any> = BaseExecutorContext & {
  /** Current node's output data (typed) */
  outputData?: TOutput;

  /** All previous node outputs by nodeId */
  nodeOutputs: Map<string, any>;

  /** Helper methods for output management */
  getOutput<T = any>(nodeId: string): T | undefined;
  setOutput(nodeId: string, output: any): void;
  hasOutput(nodeId: string): boolean;
  getAllOutputs(): Record<string, any>;
  clearOutput(nodeId: string): void;
};

/**
 * Context factory để tạo typed contexts
 */
export class ExecutorContextFactory {
  /**
   * Create typed executor context
   */
  static create<TOutput = any>(
    baseContext: NodeExecutionContext,
    options: {
      startTime?: number;
      currentRetryCount?: number;
      status?: NodeExecutionStatus;
      priority?: NodeExecutionPriority;
      isRetrying?: boolean;
      isDebugMode?: boolean;
      logger?: any;
      eventEmitter?: any;
      services?: Record<string, any>;
    } = {}
  ): ExecutorContext<TOutput> {
    const nodeOutputs = new Map<string, any>();

    // Copy existing outputs from previousOutputs
    if (baseContext.previousOutputs) {
      baseContext.previousOutputs.forEach((value, key) => {
        nodeOutputs.set(key, value);
      });
    }

    const context: ExecutorContext<TOutput> = {
      ...baseContext,
      startTime: options.startTime || Date.now(),
      currentRetryCount: options.currentRetryCount || 0,
      status: options.status || 'running',
      priority: options.priority || 'normal',
      isRetrying: options.isRetrying || false,
      isDebugMode: options.isDebugMode || false,
      logger: options.logger,
      eventEmitter: options.eventEmitter,
      nodeOutputs,

      // Helper methods
      getOutput<T = any>(nodeId: string): T | undefined {
        return nodeOutputs.get(nodeId) as T;
      },

      setOutput(nodeId: string, output: any): void {
        nodeOutputs.set(nodeId, output);
        // Also update previousOutputs for backward compatibility
        if (baseContext.previousOutputs) {
          baseContext.previousOutputs.set(nodeId, output);
        }
      },

      hasOutput(nodeId: string): boolean {
        return nodeOutputs.has(nodeId);
      },

      getAllOutputs(): Record<string, any> {
        const outputs: Record<string, any> = {};
        nodeOutputs.forEach((value, key) => {
          outputs[key] = value;
        });
        return outputs;
      },

      clearOutput(nodeId: string): void {
        nodeOutputs.delete(nodeId);
        if (baseContext.previousOutputs) {
          baseContext.previousOutputs.delete(nodeId);
        }
      },

      // Spread services
      ...options.services,
    };

    return context;
  }
}

// =================================================================
// NODE-SPECIFIC CONTEXT TYPES
// Typed contexts cho từng loại node
// =================================================================

// Import types từ interfaces
import type {
  IHttpRequestOutput,
  IIfConditionOutput,
  IWaitOutput
} from '../../../../interfaces';

/**
 * HTTP Request Executor Context
 */
export type HttpExecutorContext = ExecutorContext<IHttpRequestOutput>;

/**
 * AI Node Executor Context
 */
export type AIExecutorContext = ExecutorContext<any>; // TODO: Define IAINodeOutput

/**
 * Logic Node Executor Context
 */
export type LogicExecutorContext = ExecutorContext<IIfConditionOutput>;

/**
 * Transform Node Executor Context
 */
export type TransformExecutorContext = ExecutorContext<any>; // TODO: Define ITransformNodeOutput

/**
 * Integration Node Executor Context
 */
export type IntegrationExecutorContext = ExecutorContext<any>; // TODO: Define IIntegrationNodeOutput

/**
 * Utility Node Executor Context
 */
export type UtilityExecutorContext = ExecutorContext<IWaitOutput>;

/**
 * Union type của tất cả executor contexts
 */
export type AnyExecutorContext =
  | HttpExecutorContext
  | AIExecutorContext
  | LogicExecutorContext
  | TransformExecutorContext
  | IntegrationExecutorContext
  | UtilityExecutorContext
  | ExecutorContext<any>;

/**
 * Context type guards để check node type
 */
export class ExecutorContextGuards {
  static isHttpContext(context: AnyExecutorContext): context is HttpExecutorContext {
    return context.nodeDefinition.typeName === 'http-request';
  }

  static isAIContext(context: AnyExecutorContext): context is AIExecutorContext {
    return ['ai-chat', 'ai-completion', 'ai-embedding'].includes(context.nodeDefinition.typeName);
  }

  static isLogicContext(context: AnyExecutorContext): context is LogicExecutorContext {
    return ['if-condition', 'switch', 'loop', 'filter'].includes(context.nodeDefinition.typeName);
  }

  static isTransformContext(context: AnyExecutorContext): context is TransformExecutorContext {
    return ['edit-fields', 'merge', 'split', 'aggregate'].includes(context.nodeDefinition.typeName);
  }

  static isIntegrationContext(context: AnyExecutorContext): context is IntegrationExecutorContext {
    return ['webhook', 'email', 'sms', 'slack'].includes(context.nodeDefinition.typeName);
  }

  static isUtilityContext(context: AnyExecutorContext): context is UtilityExecutorContext {
    return ['wait', 'delay', 'random', 'uuid'].includes(context.nodeDefinition.typeName);
  }
}

/**
 * Base execution metrics
 */
export interface ExecutionMetrics {
  /** Thời gian bắt đầu */
  startTime: number;
  
  /** Thời gian kết thúc */
  endTime?: number;
  
  /** Tổng thời gian thực thi (ms) */
  executionTime?: number;
  
  /** Memory usage (bytes) */
  memoryUsage?: number;
  
  /** CPU usage (percentage) */
  cpuUsage?: number;
  
  /** Network I/O (bytes) */
  networkIO?: {
    bytesReceived: number;
    bytesSent: number;
  };
  
  /** Disk I/O (bytes) */
  diskIO?: {
    bytesRead: number;
    bytesWritten: number;
  };
  
  /** Custom metrics */
  customMetrics?: Record<string, number>;
}

/**
 * Executor capabilities
 */
export interface ExecutorCapabilities {
  /** Có hỗ trợ async execution không */
  supportsAsync: boolean;
  
  /** Có hỗ trợ streaming không */
  supportsStreaming: boolean;
  
  /** Có hỗ trợ caching không */
  supportsCaching: boolean;
  
  /** Có hỗ trợ retry không */
  supportsRetry: boolean;
  
  /** Có hỗ trợ timeout không */
  supportsTimeout: boolean;
  
  /** Có hỗ trợ cancellation không */
  supportsCancellation: boolean;
  
  /** Có hỗ trợ progress tracking không */
  supportsProgress: boolean;
  
  /** Có thread-safe không */
  isThreadSafe: boolean;
  
  /** Có stateless không */
  isStateless: boolean;
  
  /** Resource requirements */
  resourceRequirements?: {
    minMemory?: number;
    maxMemory?: number;
    minCpu?: number;
    maxCpu?: number;
    requiresNetwork?: boolean;
    requiresFileSystem?: boolean;
    requiresDatabase?: boolean;
  };
}

/**
 * Abstract base interface với common methods
 */
export interface IBaseNodeExecutor extends INodeExecutor {
  /** Executor capabilities */
  readonly capabilities: ExecutorCapabilities;
  
  /**
   * Initialize executor (được gọi khi khởi tạo)
   */
  initialize(): Promise<void>;
  
  /**
   * Destroy executor (được gọi khi shutdown)
   */
  destroy(): Promise<void>;
  
  /**
   * Health check cho executor
   */
  healthCheck(): Promise<boolean>;
  
  /**
   * Get execution metrics
   */
  getMetrics(): ExecutionMetrics;
  
  /**
   * Reset metrics
   */
  resetMetrics(): void;
}

/**
 * Factory interface để tạo executors
 */
export interface INodeExecutorFactory {
  /**
   * Tạo executor cho node type
   * @param nodeType - Type của node
   * @returns INodeExecutor instance hoặc null nếu không hỗ trợ
   */
  createExecutor(nodeType: string): INodeExecutor | null;
  
  /**
   * Đăng ký executor mới
   * @param executor - Executor instance
   */
  registerExecutor(executor: INodeExecutor): void;
  
  /**
   * Hủy đăng ký executor
   * @param nodeType - Type của node
   */
  unregisterExecutor(nodeType: string): void;
  
  /**
   * Lấy danh sách tất cả supported node types
   */
  getSupportedNodeTypes(): string[];
  
  /**
   * Kiểm tra xem có hỗ trợ node type không
   * @param nodeType - Type của node
   */
  isSupported(nodeType: string): boolean;
}

/**
 * Executor registry để quản lý tất cả executors
 */
export interface IExecutorRegistry {
  /** Map từ node type đến executor */
  readonly executors: Map<string, INodeExecutor>;
  
  /**
   * Đăng ký executor
   */
  register(executor: INodeExecutor): void;
  
  /**
   * Lấy executor cho node type
   */
  get(nodeType: string): INodeExecutor | undefined;
  
  /**
   * Kiểm tra xem có executor cho node type không
   */
  has(nodeType: string): boolean;
  
  /**
   * Hủy đăng ký executor
   */
  unregister(nodeType: string): void;
  
  /**
   * Lấy tất cả executors
   */
  getAll(): INodeExecutor[];
  
  /**
   * Clear tất cả executors
   */
  clear(): void;
}
