import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import {
  IHttpRequestParameters,
  IHttpRequestInput,
  IHttpRequestOutput,
  EHttpMethod,
  EAuthType
} from 'src/modules/workflow/interfaces';

/**
 * Shared service for HTTP operations
 * Used by HTTP_REQUEST nodes
 */
@Injectable()
export class HttpClientService {
  private readonly logger = new Logger(HttpClientService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * Execute HTTP request with retry logic
   */
  async executeHttpRequest(
    params: IHttpRequestParameters,
    input: IHttpRequestInput
  ): Promise<IHttpRequestOutput> {
    const startTime = Date.now();
    let lastError: any;

    const maxRetries = params.retry_config?.max_retries || 0;
    const retryDelay = params.retry_config?.delay || 1000;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        this.logger.debug(`HTTP request attempt ${attempt + 1}/${maxRetries + 1}: ${params.method} ${params.url}`);

        // Prepare request configuration
        const requestConfig = this.prepareRequestConfig(params, input);

        // Execute request
        const response = await firstValueFrom(
          this.httpService.request(requestConfig)
        );

        const executionTime = Date.now() - startTime;
        this.logger.debug(`HTTP request successful (${executionTime}ms): ${response.status}`);

        // Convert headers to Record<string, string>
        const headers: Record<string, string> = {};
        if (response.headers) {
          Object.entries(response.headers).forEach(([key, value]) => {
            headers[key] = String(value);
          });
        }

        return {
          status_code: response.status,
          status_text: response.statusText || '',
          headers,
          body: response.data,
          body_text: typeof response.data === 'string' ? response.data : JSON.stringify(response.data),
          response_time: executionTime,
          final_url: response.config?.url || params.url,
          success: response.status >= 200 && response.status < 300,
          metadata: {
            request_id: this.generateRequestId(),
            timestamp: Date.now(),
            redirects_count: 0, // Axios doesn't provide this easily
            content_length: this.calculateResponseSize(response.data),
            content_type: headers['content-type'] || 'unknown'
          }
        };

      } catch (error) {
        lastError = error;
        const isLastAttempt = attempt === maxRetries;

        this.logger.warn(`HTTP request failed (attempt ${attempt + 1}): ${error.message}`);

        if (isLastAttempt || !this.shouldRetry(error, params)) {
          break;
        }

        // Wait before retry
        if (attempt < maxRetries) {
          const backoffFactor = params.retry_config?.backoff_factor || 2;
          await this.sleep(retryDelay * Math.pow(backoffFactor, attempt));
        }
      }
    }

    // All attempts failed
    const executionTime = Date.now() - startTime;
    this.logger.error(`HTTP request failed after ${maxRetries + 1} attempts: ${lastError.message}`);

    // Convert error headers to Record<string, string>
    const errorHeaders: Record<string, string> = {};
    if (lastError.response?.headers) {
      Object.entries(lastError.response.headers).forEach(([key, value]) => {
        errorHeaders[key] = String(value);
      });
    }

    return {
      status_code: lastError.response?.status || 0,
      status_text: lastError.response?.statusText || 'Request Failed',
      headers: errorHeaders,
      body: lastError.response?.data || null,
      body_text: lastError.message,
      response_time: executionTime,
      final_url: params.url,
      success: false,
      metadata: {
        request_id: this.generateRequestId(),
        timestamp: Date.now(),
        redirects_count: 0,
        content_length: 0,
        content_type: 'error'
      }
    };
  }

  /**
   * Prepare request configuration
   */
  private prepareRequestConfig(
    params: IHttpRequestParameters,
    input: IHttpRequestInput
  ): any {
    const config: any = {
      method: params.method,
      url: this.buildUrl(params.url, input),
      timeout: params.timeout || 30000,
      headers: {
        'User-Agent': 'Workflow-Engine/1.0',
        ...params.headers
      }
    };

    // Set content type
    if (params.content_type) {
      config.headers['Content-Type'] = params.content_type;
    }

    // Add authentication
    this.addAuthentication(config, params);

    // Add request body
    if (params.body && (params.method !== EHttpMethod.GET && params.method !== EHttpMethod.HEAD)) {
      if (typeof params.body === 'string') {
        config.data = params.body;
      } else {
        config.data = params.body;
        if (!config.headers['Content-Type']) {
          config.headers['Content-Type'] = 'application/json';
        }
      }
    }

    // SSL and redirect settings
    if (params.verify_ssl === false) {
      config.httpsAgent = new (require('https').Agent)({
        rejectUnauthorized: false
      });
    }

    if (params.follow_redirects === false) {
      config.maxRedirects = 0;
    }

    return config;
  }

  /**
   * Add authentication to request
   */
  private addAuthentication(config: any, params: IHttpRequestParameters): void {
    if (!params.auth_config) {
      return;
    }

    switch (params.auth_type) {
      case EAuthType.BASIC:
        if (params.auth_config.username && params.auth_config.password) {
          config.auth = {
            username: params.auth_config.username,
            password: params.auth_config.password
          };
        }
        break;

      case EAuthType.BEARER:
        if (params.auth_config.token) {
          config.headers['Authorization'] = `Bearer ${params.auth_config.token}`;
        }
        break;

      case EAuthType.API_KEY:
        if (params.auth_config.api_key) {
          const headerName = params.auth_config.api_key_header || 'X-API-Key';
          config.headers[headerName] = params.auth_config.api_key;
        }
        break;

      case EAuthType.OAUTH2:
        if (params.auth_config.token) {
          config.headers['Authorization'] = `Bearer ${params.auth_config.token}`;
        }
        break;

      case EAuthType.NONE:
      default:
        // No authentication
        break;
    }
  }

  /**
   * Build URL with query parameters
   */
  private buildUrl(
    baseUrl: string,
    input: IHttpRequestInput
  ): string {
    let url = baseUrl;

    // Replace URL parameters
    if (input.url_params) {
      for (const [key, value] of Object.entries(input.url_params)) {
        url = url.replace(`{${key}}`, encodeURIComponent(value));
      }
    }

    // Add query parameters
    if (input.query_params && Object.keys(input.query_params).length > 0) {
      const searchParams = new URLSearchParams();

      for (const [key, value] of Object.entries(input.query_params)) {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      }

      const queryString = searchParams.toString();
      if (queryString) {
        url += (url.includes('?') ? '&' : '?') + queryString;
      }
    }

    return url;
  }

  /**
   * Check if error should trigger retry
   */
  private shouldRetry(error: any, params: IHttpRequestParameters): boolean {
    if (!params.retry_config?.enabled) {
      return false;
    }

    // Retry on network errors
    const networkErrors = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND'];
    if (networkErrors.some(code => error.code === code || error.message.includes(code))) {
      return true;
    }

    // Retry on specific HTTP status codes (5xx, 408, 429)
    if (error.response?.status) {
      const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
      return retryableStatusCodes.includes(error.response.status);
    }

    return false;
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Calculate response size in bytes
   */
  private calculateResponseSize(data: any): number {
    if (typeof data === 'string') {
      return Buffer.byteLength(data, 'utf8');
    }

    if (typeof data === 'object') {
      return Buffer.byteLength(JSON.stringify(data), 'utf8');
    }

    return 0;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
