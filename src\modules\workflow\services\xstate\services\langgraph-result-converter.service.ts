import { Injectable, Logger } from '@nestjs/common';
import { 
  LangGraphExecutionResult,
  DetailedNodeExecutionResult,
  NodeExecutionContext 
} from '../types';

/**
 * LangGraph raw result structure (từ actual LangGraph API)
 */
export interface LangGraphRawResult {
  /** Status của execution */
  status: 'success' | 'error' | 'timeout' | 'cancelled';
  
  /** Result data từ agent */
  data?: any;
  
  /** Messages từ conversation */
  messages?: Array<{
    id?: string;
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    timestamp?: number;
    metadata?: Record<string, any>;
  }>;
  
  /** Thread/session information */
  thread?: {
    id: string;
    created_at: number;
    updated_at: number;
    metadata?: Record<string, any>;
  };
  
  /** Checkpoint information */
  checkpoint?: {
    id: string;
    thread_id: string;
    created_at: number;
    state: any;
    metadata?: Record<string, any>;
  };
  
  /** Error information */
  error?: {
    type: string;
    message: string;
    code?: string;
    details?: any;
  };
  
  /** Execution metadata */
  metadata?: {
    /** Execution time in milliseconds */
    execution_time?: number;
    
    /** Token usage */
    token_usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
    
    /** Model information */
    model?: {
      name: string;
      version?: string;
      provider?: string;
    };
    
    /** Tools used during execution */
    tools_used?: Array<{
      name: string;
      input: any;
      output: any;
      execution_time?: number;
    }>;
    
    /** Custom metadata từ agent */
    custom?: Record<string, any>;
  };
}

/**
 * Conversion options
 */
export interface ConversionOptions {
  /** Có include raw LangGraph data không */
  includeRawData?: boolean;
  
  /** Có sanitize sensitive data không */
  sanitizeSensitiveData?: boolean;
  
  /** Có compress large messages không */
  compressLargeMessages?: boolean;
  
  /** Max message length trước khi compress */
  maxMessageLength?: number;
  
  /** Có include debug information không */
  includeDebugInfo?: boolean;
  
  /** Custom field mappings */
  fieldMappings?: Record<string, string>;
}

/**
 * Service để convert LangGraph results thành XState-compatible format
 */
@Injectable()
export class LangGraphResultConverterService {
  private readonly logger = new Logger(LangGraphResultConverterService.name);
  
  /**
   * Convert LangGraph raw result thành LangGraphExecutionResult
   */
  convertToLangGraphResult(
    rawResult: LangGraphRawResult,
    options: ConversionOptions = {}
  ): LangGraphExecutionResult {
    try {
      const success = rawResult.status === 'success';
      
      // Convert messages
      const messages = this.convertMessages(rawResult.messages || [], options);
      
      // Convert metadata
      const metadata = this.convertMetadata(rawResult.metadata, rawResult.thread, rawResult.checkpoint);
      
      // Handle error
      const error = rawResult.error ? new Error(rawResult.error.message) : undefined;
      
      const result: LangGraphExecutionResult = {
        success,
        result: rawResult.data,
        messages,
        error,
        metadata,
      };
      
      this.logger.debug(`Converted LangGraph result: ${success ? 'SUCCESS' : 'FAILED'}`);
      
      return result;
      
    } catch (error) {
      this.logger.error('Failed to convert LangGraph result:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Conversion failed'),
        metadata: {
          threadId: 'unknown',
          executionTime: 0,
        },
      };
    }
  }
  
  /**
   * Convert LangGraphExecutionResult thành DetailedNodeExecutionResult
   */
  convertToNodeExecutionResult(
    langGraphResult: LangGraphExecutionResult,
    context: NodeExecutionContext,
    startTime: number,
    options: ConversionOptions = {}
  ): DetailedNodeExecutionResult {
    try {
      const executionTime = langGraphResult.metadata?.executionTime || (Date.now() - startTime);
      
      if (!langGraphResult.success) {
        return {
          success: false,
          error: langGraphResult.error,
          shouldRetry: this.shouldRetryBasedOnError(langGraphResult.error),
          metadata: {
            executionTime,
            logs: [
              `LangGraph execution failed: ${langGraphResult.error?.message}`,
              `Agent: ${context.node.agentId}`,
              `Thread: ${langGraphResult.metadata?.threadId}`,
            ],
          },
        };
      }
      
      // Prepare output data
      const outputData = this.prepareOutputData(langGraphResult, options);
      
      // Prepare metadata
      const metadata = this.prepareNodeExecutionMetadata(
        langGraphResult,
        context,
        executionTime,
        options
      );
      
      const result: DetailedNodeExecutionResult = {
        success: true,
        outputData,
        metadata,
      };
      
      this.logger.debug(`Converted to node execution result for ${context.node.id}`);
      
      return result;
      
    } catch (error) {
      this.logger.error(`Failed to convert to node execution result for ${context.node.id}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Node result conversion failed'),
        metadata: {
          executionTime: Date.now() - startTime,
          logs: [`Conversion failed: ${error}`],
        },
      };
    }
  }
  
  /**
   * Batch convert multiple LangGraph results
   */
  batchConvertToNodeResults(
    results: Array<{
      langGraphResult: LangGraphExecutionResult;
      context: NodeExecutionContext;
      startTime: number;
    }>,
    options: ConversionOptions = {}
  ): DetailedNodeExecutionResult[] {
    return results.map(({ langGraphResult, context, startTime }) =>
      this.convertToNodeExecutionResult(langGraphResult, context, startTime, options)
    );
  }
  
  /**
   * Extract conversation summary từ messages
   */
  extractConversationSummary(messages: any[]): {
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    toolMessages: number;
    lastMessage?: {
      role: string;
      content: string;
      timestamp?: number;
    };
    conversationLength: number;
  } {
    const summary = {
      totalMessages: messages.length,
      userMessages: 0,
      assistantMessages: 0,
      toolMessages: 0,
      conversationLength: 0,
    };
    
    let lastMessage;
    
    for (const message of messages) {
      switch (message.role) {
        case 'user':
          summary.userMessages++;
          break;
        case 'assistant':
          summary.assistantMessages++;
          break;
        case 'tool':
          summary.toolMessages++;
          break;
      }
      
      if (message.content) {
        summary.conversationLength += message.content.length;
      }
      
      lastMessage = message;
    }
    
    return {
      ...summary,
      lastMessage,
    };
  }
  
  /**
   * Sanitize sensitive data từ result
   */
  sanitizeResult(result: any, sensitiveFields: string[] = []): any {
    const defaultSensitiveFields = [
      'password',
      'token',
      'key',
      'secret',
      'credential',
      'auth',
      'api_key',
      'access_token',
    ];
    
    const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields];
    
    return this.recursiveSanitize(result, allSensitiveFields);
  }
  
  // Private helper methods
  
  private convertMessages(
    rawMessages: LangGraphRawResult['messages'],
    options: ConversionOptions
  ): any[] {
    if (!rawMessages) return [];
    
    return rawMessages.map(msg => {
      let content = msg.content;
      
      // Compress large messages if needed
      if (options.compressLargeMessages && options.maxMessageLength) {
        if (content.length > options.maxMessageLength) {
          content = content.substring(0, options.maxMessageLength) + '... [truncated]';
        }
      }
      
      // Sanitize if needed
      if (options.sanitizeSensitiveData) {
        content = this.sanitizeText(content);
      }
      
      return {
        id: msg.id,
        role: msg.role,
        content,
        timestamp: msg.timestamp || Date.now(),
        metadata: msg.metadata,
      };
    });
  }
  
  private convertMetadata(
    rawMetadata: LangGraphRawResult['metadata'],
    thread: LangGraphRawResult['thread'],
    checkpoint: LangGraphRawResult['checkpoint']
  ): LangGraphExecutionResult['metadata'] {
    return {
      threadId: thread?.id || 'unknown',
      checkpointId: checkpoint?.id,
      executionTime: rawMetadata?.execution_time || 0,
      tokenUsage: rawMetadata?.token_usage ? {
        inputTokens: rawMetadata.token_usage.prompt_tokens,
        outputTokens: rawMetadata.token_usage.completion_tokens,
        totalTokens: rawMetadata.token_usage.total_tokens,
      } : undefined,
      agentConfig: rawMetadata?.model ? {
        modelName: rawMetadata.model.name,
        temperature: 0, // Default, should be extracted from actual config
        maxTokens: 0,   // Default, should be extracted from actual config
      } : undefined,
      toolsUsed: rawMetadata?.tools_used?.map(tool => tool.name) || [],
      custom: rawMetadata?.custom,
    };
  }
  
  private prepareOutputData(
    langGraphResult: LangGraphExecutionResult,
    options: ConversionOptions
  ): any {
    const outputData: any = {
      result: langGraphResult.result,
      messages: langGraphResult.messages,
      threadId: langGraphResult.metadata?.threadId,
      checkpointId: langGraphResult.metadata?.checkpointId,
    };
    
    // Include conversation summary
    if (langGraphResult.messages) {
      outputData.conversationSummary = this.extractConversationSummary(langGraphResult.messages);
    }
    
    // Include raw data if requested
    if (options.includeRawData) {
      outputData._raw = langGraphResult;
    }
    
    // Apply field mappings
    if (options.fieldMappings) {
      for (const [from, to] of Object.entries(options.fieldMappings)) {
        if (outputData[from] !== undefined) {
          outputData[to] = outputData[from];
          delete outputData[from];
        }
      }
    }
    
    // Sanitize if needed
    if (options.sanitizeSensitiveData) {
      return this.sanitizeResult(outputData);
    }
    
    return outputData;
  }
  
  private prepareNodeExecutionMetadata(
    langGraphResult: LangGraphExecutionResult,
    context: NodeExecutionContext,
    executionTime: number,
    options: ConversionOptions
  ): DetailedNodeExecutionResult['metadata'] {
    const logs = [
      `LangGraph execution completed successfully`,
      `Agent: ${context.node.agentId}`,
      `Thread: ${langGraphResult.metadata?.threadId}`,
      `Execution time: ${executionTime}ms`,
    ];
    
    if (langGraphResult.metadata?.tokenUsage) {
      logs.push(`Tokens used: ${langGraphResult.metadata.tokenUsage.totalTokens}`);
    }
    
    if (langGraphResult.messages) {
      logs.push(`Messages: ${langGraphResult.messages.length}`);
    }
    
    const metadata: DetailedNodeExecutionResult['metadata'] = {
      executionTime,
      customMetrics: {
        agentId: context.node.agentId,
        threadId: langGraphResult.metadata?.threadId,
        checkpointId: langGraphResult.metadata?.checkpointId,
        messagesCount: langGraphResult.messages?.length || 0,
        toolsUsed: langGraphResult.metadata?.toolsUsed?.length || 0,
        tokenUsage: langGraphResult.metadata?.tokenUsage, // Move to customMetrics
      },
      logs,
    };
    
    // Include debug info if requested
    if (options.includeDebugInfo) {
      metadata.debug = {
        langGraphMetadata: langGraphResult.metadata,
        nodeContext: {
          nodeId: context.node.id,
          nodeName: context.node.name,
          agentId: context.node.agentId,
        },
        conversionOptions: options,
      };
    }
    
    return metadata;
  }
  
  private shouldRetryBasedOnError(error?: Error): boolean {
    if (!error) return false;
    
    const retryableErrors = [
      'timeout',
      'rate_limit',
      'temporary_failure',
      'network_error',
      'service_unavailable',
      'internal_error',
    ];
    
    const errorMessage = error.message.toLowerCase();
    
    return retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError)
    );
  }
  
  private sanitizeText(text: string): string {
    // Basic sanitization - remove potential sensitive patterns
    return text
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
      .replace(/\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, '[CARD]')
      .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]')
      .replace(/\b(?:sk-|pk_)[a-zA-Z0-9]{20,}\b/g, '[API_KEY]');
  }
  
  private recursiveSanitize(obj: any, sensitiveFields: string[]): any {
    if (obj === null || obj === undefined) {
      return obj;
    }
    
    if (typeof obj === 'string') {
      return this.sanitizeText(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.recursiveSanitize(item, sensitiveFields));
    }
    
    if (typeof obj === 'object') {
      const sanitized: any = {};
      
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        
        if (sensitiveFields.some(field => lowerKey.includes(field))) {
          sanitized[key] = '[REDACTED]';
        } else {
          sanitized[key] = this.recursiveSanitize(value, sensitiveFields);
        }
      }
      
      return sanitized;
    }
    
    return obj;
  }
}
