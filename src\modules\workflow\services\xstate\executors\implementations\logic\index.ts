// Logic Executors
export { IfConditionExecutor } from './if-condition.executor';
export { LoopExecutor } from './loop.executor';
export { SwitchExecutor } from './switch.executor';
export { WaitExecutor } from './wait.executor';

// Import for array
import { IfConditionExecutor } from './if-condition.executor';
import { LoopExecutor } from './loop.executor';
import { SwitchExecutor } from './switch.executor';
import { WaitExecutor } from './wait.executor';

// Export all logic executors as array for easy registration
export const LOGIC_EXECUTORS = [
  IfConditionExecutor,
  LoopExecutor,
  SwitchExecutor,
  WaitExecutor,
];