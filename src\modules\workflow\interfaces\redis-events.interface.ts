/**
 * Redis Event Types cho Workflow System
 * Định nghĩa các event types được publish qua Redis
 */

export interface BaseRedisEvent {
  type: string;
  testId?: string;
  executionId?: string;
  userId: number;
  timestamp: number;
}

/**
 * Node completion event - MINIMAL payload
 */
export interface NodeCompletedEvent extends BaseRedisEvent {
  type: 'node_completed';
  nodeId: string;
  nodeResult: {
    success: boolean;
    output?: any;
    error?: any;
    executionTime: number;
  };
}

/**
 * Workflow progress summary - SEPARATE from node events
 */
export interface WorkflowProgressEvent extends BaseRedisEvent {
  type: 'workflow_progress';
  summary: {
    completedNodes: number;
    totalNodes: number;
    runningNodes: number;
    failedNodes: number;
    percentage: number;
  };
}

/**
 * Test progress event - MINIMAL data
 */
export interface TestProgressEvent extends BaseRedisEvent {
  type: 'test_progress';
  currentStep: number;
  totalSteps: number;
  percentage: number;
  description?: string;
  nodeId?: string;
  nodeResult?: any;
}

/**
 * Test started event
 */
export interface TestStartedEvent extends BaseRedisEvent {
  type: 'test_started';
  data: {
    workflowId: string;
    nodeId?: string;
    mode: 'single_node' | 'full_workflow' | 'xstate_workflow';
    totalNodes?: number;
  };
}

/**
 * Test completed event
 */
export interface TestCompletedEvent extends BaseRedisEvent {
  type: 'test_completed';
  result: {
    success: boolean;
    output?: any;
    error?: any;
    executionTime: number;
    nodesExecuted: number;
    totalNodes: number;
  };
}

/**
 * Execution started event
 */
export interface ExecutionStartedEvent extends BaseRedisEvent {
  type: 'execution_started';
  data: {
    workflowId: string;
    mode: 'single_node' | 'full_workflow' | 'xstate_workflow';
    totalNodes?: number;
  };
}

/**
 * Execution progress event
 */
export interface ExecutionProgressEvent extends BaseRedisEvent {
  type: 'execution_progress';
  progress: {
    currentStep: number;
    totalSteps: number;
    percentage: number;
    description?: string;
    nodeId?: string;
  };
}

/**
 * Execution completed event
 */
export interface ExecutionCompletedEvent extends BaseRedisEvent {
  type: 'execution_completed';
  result: {
    success: boolean;
    output?: any;
    error?: any;
    executionTime: number;
    nodesExecuted: number;
    totalNodes: number;
  };
}

/**
 * Error event
 */
export interface ErrorEvent extends BaseRedisEvent {
  type: 'test_error' | 'execution_error';
  error: {
    message: string;
    code: string;
    stack?: string;
  };
}

/**
 * Node spawned event - khi nodes được spawn
 */
export interface NodesSpawnedEvent extends BaseRedisEvent {
  type: 'nodes_spawned';
  nodeId: string; // Reference node
  nodeResult: {
    type: 'nodes_spawned';
    spawnedNodesCount: number;
    spawnedNodes: string[];
  };
}

/**
 * Dependencies resolved event - khi dependencies được resolve
 */
export interface DependenciesResolvedEvent extends BaseRedisEvent {
  type: 'dependencies_resolved';
  nodeId: string; // Reference node
  nodeResult: {
    type: 'dependencies_resolved';
    readyNodesCount: number;
    readyNodes: string[];
  };
}

/**
 * Union type cho tất cả Redis events
 */
export type RedisWorkflowEvent = 
  | NodeCompletedEvent
  | WorkflowProgressEvent
  | TestProgressEvent
  | TestStartedEvent
  | TestCompletedEvent
  | ExecutionStartedEvent
  | ExecutionProgressEvent
  | ExecutionCompletedEvent
  | ErrorEvent
  | NodesSpawnedEvent
  | DependenciesResolvedEvent;

/**
 * Redis channels
 */
export const REDIS_CHANNELS = {
  TEST_PROGRESS: (userId: number) => `workflow_test_progress:${userId}`,
  EXECUTION_STATUS: (userId: number) => `workflow_execution_status:${userId}`,
  NODE_UPDATES: (userId: number) => `workflow_node_updates:${userId}`,
  WORKFLOW_SUMMARY: (userId: number) => `workflow_summary:${userId}`,
} as const;

/**
 * Redis keys
 */
export const REDIS_KEYS = {
  TEST_EXECUTION: (testId: string) => `test_execution:${testId}`,
  TEST_PROGRESS: (testId: string) => `test_execution:${testId}:progress`,
  TEST_RESULT: (testId: string) => `test_execution:${testId}:result`,
  TEST_NODE: (testId: string, nodeId: string) => `test_execution:${testId}:nodes:${nodeId}`,
  TEST_SUMMARY: (testId: string) => `test_execution:${testId}:summary`,
  
  EXECUTION: (executionId: string) => `execution:${executionId}`,
  EXECUTION_PROGRESS: (executionId: string) => `execution:${executionId}:progress`,
  EXECUTION_RESULT: (executionId: string) => `execution:${executionId}:result`,
  EXECUTION_NODE: (executionId: string, nodeId: string) => `execution:${executionId}:nodes:${nodeId}`,
  
  USER_TESTS: (userId: number) => `user_tests:${userId}`,
  USER_EXECUTIONS: (userId: number) => `user_executions:${userId}`,
} as const;

/**
 * TTL values
 */
export const REDIS_TTL = {
  TEST_DATA: 3600, // 1 hour
  EXECUTION_DATA: 86400, // 24 hours
  PROGRESS_DATA: 1800, // 30 minutes
  SUMMARY_DATA: 7200, // 2 hours
} as const;
