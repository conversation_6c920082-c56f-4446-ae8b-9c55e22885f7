import { Injectable, Logger } from '@nestjs/common';
import {
    NodeCompletedEvent,
    WorkflowProgressEvent,
    TestStartedEvent,
    TestCompletedEvent,
    ExecutionStartedEvent,
    ExecutionCompletedEvent,
    REDIS_CHANNELS,
    REDIS_KEYS,
    REDIS_TTL
} from '../interfaces/redis-events.interface';

/**
 * Redis Publisher Service for real-time workflow updates
 * Publishes test và execution events để BE app có thể subscribe
 */
@Injectable()
export class RedisPublisherService {
    private readonly logger = new Logger(RedisPublisherService.name);
    
    // Mock Redis client - trong thực tế sẽ inject Redis client
    private redis: any = {
        publish: async (channel: string, message: string) => {
            this.logger.log(`Publishing to ${channel}: ${message}`);
            // Actual Redis publish implementation
        },
        setex: async (key: string, ttl: number, value: string) => {
            this.logger.log(`Setting ${key} with TTL ${ttl}: ${value}`);
            // Actual Redis setex implementation
        }
    };

    /**
     * Publish test started event
     */
    async publishTestStarted(testId: string, userId: number, data: any) {
        const channel = REDIS_CHANNELS.TEST_PROGRESS(userId);
        const event: TestStartedEvent = {
            type: 'test_started',
            testId,
            userId,
            timestamp: Date.now(),
            data
        };

        await this.redis.publish(channel, JSON.stringify(event));

        // Store test session info
        const testKey = REDIS_KEYS.TEST_EXECUTION(testId);
        await this.redis.setex(testKey, REDIS_TTL.TEST_DATA, JSON.stringify({
            testId,
            userId,
            status: 'running',
            startTime: Date.now(),
            ...data
        }));
    }

    /**
     * Publish test progress update - MINIMAL payload
     */
    async publishTestProgress(testId: string, userId: number, progress: {
        currentStep: number;
        totalSteps: number;
        percentage: number;
        description?: string;
        nodeId?: string;
        nodeResult?: any;
    }) {
        const channel = `workflow_test_progress:${userId}`;

        // Minimal message - chỉ gửi thông tin cần thiết
        const message = JSON.stringify({
            type: 'test_progress',
            testId,
            userId,
            timestamp: Date.now(),
            currentStep: progress.currentStep,
            totalSteps: progress.totalSteps,
            percentage: progress.percentage,
            description: progress.description,
            nodeId: progress.nodeId,
            nodeResult: progress.nodeResult
        });

        await this.redis.publish(channel, message);

        // Store node result separately if provided
        if (progress.nodeId && progress.nodeResult) {
            const nodeKey = `test_execution:${testId}:nodes:${progress.nodeId}`;
            await this.redis.setex(nodeKey, 3600, JSON.stringify({
                nodeId: progress.nodeId,
                result: progress.nodeResult,
                timestamp: Date.now()
            }));
        }
    }

    /**
     * Publish node completion event - FOCUSED on node data
     */
    async publishNodeCompleted(testId: string, userId: number, nodeData: {
        nodeId: string;
        success: boolean;
        output?: any;
        error?: any;
        executionTime: number;
    }) {
        const channel = REDIS_CHANNELS.TEST_PROGRESS(userId);
        const event: NodeCompletedEvent = {
            type: 'node_completed',
            testId,
            userId,
            timestamp: Date.now(),
            nodeId: nodeData.nodeId,
            nodeResult: {
                success: nodeData.success,
                output: nodeData.output,
                error: nodeData.error,
                executionTime: nodeData.executionTime
            }
        };

        await this.redis.publish(channel, JSON.stringify(event));

        // Store individual node result
        const nodeKey = REDIS_KEYS.TEST_NODE(testId, nodeData.nodeId);
        await this.redis.setex(nodeKey, REDIS_TTL.TEST_DATA, JSON.stringify({
            nodeId: nodeData.nodeId,
            result: nodeData,
            completedAt: Date.now()
        }));
    }

    /**
     * Publish workflow progress summary - SEPARATE from node events
     */
    async publishWorkflowProgress(testId: string, userId: number, summary: {
        completedNodes: number;
        totalNodes: number;
        runningNodes: number;
        failedNodes: number;
        percentage: number;
    }) {
        const channel = REDIS_CHANNELS.TEST_PROGRESS(userId);
        const event: WorkflowProgressEvent = {
            type: 'workflow_progress',
            testId,
            userId,
            timestamp: Date.now(),
            summary
        };

        await this.redis.publish(channel, JSON.stringify(event));

        // Store workflow summary
        const summaryKey = REDIS_KEYS.TEST_SUMMARY(testId);
        await this.redis.setex(summaryKey, REDIS_TTL.SUMMARY_DATA, JSON.stringify({
            ...summary,
            updatedAt: Date.now()
        }));
    }

    /**
     * Publish test completed event
     */
    async publishTestCompleted(testId: string, userId: number, result: {
        success: boolean;
        output?: any;
        error?: any;
        executionTime: number;
        nodesExecuted: number;
        totalNodes: number;
    }) {
        const channel = `workflow_test_progress:${userId}`;
        const message = JSON.stringify({
            type: 'test_completed',
            testId,
            userId,
            timestamp: Date.now(),
            result
        });

        await this.redis.publish(channel, message);

        // Store final test result
        const resultKey = `test_execution:${testId}:result`;
        await this.redis.setex(resultKey, 3600, JSON.stringify({
            ...result,
            completedAt: Date.now()
        }));

        // Update test status
        const testKey = `test_execution:${testId}`;
        const testData = JSON.stringify({
            testId,
            userId,
            status: result.success ? 'completed' : 'failed',
            startTime: Date.now() - result.executionTime,
            endTime: Date.now(),
            result
        });
        await this.redis.setex(testKey, 3600, testData);
    }

    /**
     * Publish execution started event (for execute mode)
     */
    async publishExecutionStarted(executionId: string, userId: number, data: any) {
        const channel = `workflow_execution_status:${userId}`;
        const message = JSON.stringify({
            type: 'execution_started',
            executionId,
            userId,
            timestamp: Date.now(),
            data
        });

        await this.redis.publish(channel, message);
    }

    /**
     * Publish execution progress (for execute mode)
     */
    async publishExecutionProgress(executionId: string, userId: number, progress: any) {
        const channel = `workflow_execution_status:${userId}`;
        const message = JSON.stringify({
            type: 'execution_progress',
            executionId,
            userId,
            timestamp: Date.now(),
            progress
        });

        await this.redis.publish(channel, message);

        // Cache execution progress
        const progressKey = `execution:${executionId}:progress`;
        await this.redis.setex(progressKey, 86400, JSON.stringify(progress)); // 24h TTL
    }

    /**
     * Publish execution completed event (for execute mode)
     */
    async publishExecutionCompleted(executionId: string, userId: number, result: any) {
        const channel = `workflow_execution_status:${userId}`;
        const message = JSON.stringify({
            type: 'execution_completed',
            executionId,
            userId,
            timestamp: Date.now(),
            result
        });

        await this.redis.publish(channel, message);
    }

    /**
     * Publish error event
     */
    async publishError(id: string, userId: number, error: any, type: 'test' | 'execution') {
        const channel = type === 'test' 
            ? `workflow_test_progress:${userId}`
            : `workflow_execution_status:${userId}`;
            
        const message = JSON.stringify({
            type: `${type}_error`,
            [type === 'test' ? 'testId' : 'executionId']: id,
            userId,
            timestamp: Date.now(),
            error: {
                message: error.message,
                code: error.code || 'UNKNOWN_ERROR',
                stack: error.stack
            }
        });

        await this.redis.publish(channel, message);
    }

    /**
     * Get test result from Redis
     */
    async getTestResult(testId: string): Promise<any> {
        const resultKey = `test_execution:${testId}:result`;
        // Mock implementation - actual would use redis.get()
        return null;
    }

    /**
     * Get execution progress from Redis
     */
    async getExecutionProgress(executionId: string): Promise<any> {
        const progressKey = `execution:${executionId}:progress`;
        // Mock implementation - actual would use redis.get()
        return null;
    }

    /**
     * Cleanup test data (called after TTL expires or manual cleanup)
     */
    async cleanupTestData(testId: string) {
        const keys = [
            `test_execution:${testId}`,
            `test_execution:${testId}:progress`,
            `test_execution:${testId}:result`,
            `test_execution:${testId}:nodes:*`
        ];

        // Mock implementation - actual would use redis.del()
        this.logger.log(`Cleaning up test data for ${testId}`);
    }
}
