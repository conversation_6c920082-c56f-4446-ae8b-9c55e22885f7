// src/redis/redis.service.ts
import { Injectable, OnModuleDestroy, Logger } from '@nestjs/common';
import Redis from 'ioredis';
import { env } from '../../config';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private readonly redis: Redis;

  constructor() {
    const url = env.external.REDIS_URL;
    this.redis = new Redis(url, {
      // Connection options for better reliability
      connectTimeout: 10000, // 10 seconds
      lazyConnect: true, // Don't connect immediately

      // --- Reconnection/Retry Strategy ---

      // Allow commands to be retried indefinitely if the connection is lost.
      maxRetriesPerRequest: null,

      /**
       * Defines the reconnection strategy.
       * @param times The number of times the connection has been retried.
       * @returns The delay in milliseconds before the next attempt.
       */
      retryStrategy(times: number): number {
        // Exponential backoff with a cap
        const delay = Math.min(times * 100, 3000); // 100ms, 200ms, 300ms, ..., up to 3s
        this.logger.log(
          `Redis connection retry attempt ${times}, retrying in ${delay}ms...`,
        );
        return delay;
      },
    });

    // Connection event handlers
    this.redis.on('connect', () => {
      this.logger.log('Redis connected successfully');
    });

    this.redis.on('ready', () => {
      this.logger.log('Redis ready for commands');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis connection error:', error);
    });

    this.redis.on('close', () => {
      this.logger.warn('Redis connection closed');
    });

    this.redis.on('reconnecting', () => {
      this.logger.log('Redis reconnecting...');
    });

    // Initial connection
    this.connectToRedis();
  }

  private async connectToRedis() {
    try {
      await this.redis.connect();
      this.logger.log('Initial Redis connection established');
    } catch (error) {
      this.logger.error('Failed to establish initial Redis connection:', error);
    }
  }

  /**
   * Manually reconnect to Redis
   */
  async reconnect(): Promise<void> {
    try {
      this.logger.log('Attempting manual Redis reconnection...');
      await this.redis.disconnect();
      await this.redis.connect();
      this.logger.log('Manual Redis reconnection successful');
    } catch (error) {
      this.logger.error('Manual Redis reconnection failed:', error);
      throw error;
    }
  }

  getClient() {
    return this.redis;
  }

  async xadd(
    stream: string,
    data: Record<string, string>,
  ): Promise<string | null> {
    return this.redis.xadd(stream, '*', ...Object.entries(data).flat());
  }

  async xread(stream: string, lastId: string = '$'): Promise<any> {
    return this.redis.xread('BLOCK', 0, 'STREAMS', stream, lastId);
  }

  // Basic Redis operations
  async get(key: string): Promise<string | null> {
    return this.redis.get(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<string> {
    if (ttl) {
      return this.redis.setex(key, ttl, value);
    }
    return this.redis.set(key, value);
  }

  /**
   * Delete one or more keys
   * @param keys Keys to delete
   * @returns Number of keys that were deleted
   */
  async del(...keys: string[]): Promise<number> {
    return this.redis.del(...keys);
  }

  /**
   * Check if a key exists
   * @param key Redis key
   * @returns True if key exists, false otherwise
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  /**
   * Push one or more values to the left (head) of a list
   * @param key Redis list key
   * @param values Values to push
   * @returns Length of the list after the push operation
   */
  async lpush(key: string, ...values: string[]): Promise<number> {
    return this.redis.lpush(key, ...values);
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    return this.redis.lrange(key, start, stop);
  }

  async lrem(key: string, count: number, value: string): Promise<number> {
    return this.redis.lrem(key, count, value);
  }

  async ltrim(key: string, start: number, stop: number): Promise<string> {
    return this.redis.ltrim(key, start, stop);
  }

  /**
   * Get the length of a list
   * @param key Redis list key
   * @returns Length of the list
   */
  async llen(key: string): Promise<number> {
    return this.redis.llen(key);
  }

  // TTL operations
  async expire(key: string, seconds: number): Promise<number> {
    return this.redis.expire(key, seconds);
  }

  async ttl(key: string): Promise<number> {
    return this.redis.ttl(key);
  }

  getRawClient() {
    return this.redis;
  }

  onModuleDestroy() {
    this.redis.disconnect();
  }
}
