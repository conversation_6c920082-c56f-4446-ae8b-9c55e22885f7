import {
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Node } from './node.entity';
import { Workflow } from './workflow.entity';

@Entity('webhook_registry')
@Index('webhook_registry_pk', ['webhookName', 'nodeId', 'workflowId'], { unique: true })
export class WebhookRegistry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'webhook_name', type: 'varchar', length: 255 })
  webhookName: string;

  @Column({ name: 'node_id', type: 'uuid', nullable: true })
  nodeId: string;

  @Column({ name: 'workflow_id', type: 'uuid', nullable: true })
  workflowId: string;

  @Column({ name: 'path_length', type: 'int', nullable: true, default: 0 })
  pathLength: number;

  @Column({ name: 'webhook_path', type: 'varchar', length: 500, nullable: true })
  webhookPath: string;

  @Column({ name: 'method', type: 'varchar', length: 10, nullable: true, default: 'POST' })
  method: string;

  @Column({ name: 'node_name', type: 'varchar', length: 255, nullable: true })
  nodeName: string;

  // Relations
  @ManyToOne(() => Node, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'node_id' })
  node: Node;

  @ManyToOne(() => Workflow, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workflow_id' })
  workflow: Workflow;
}
