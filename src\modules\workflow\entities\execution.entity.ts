import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
} from 'typeorm';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';

@Entity('executions')
export class Execution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'workflow_id', type: 'uuid' })
  workflowId: string;

  @Column({
    type: 'enum',
    enum: ExecutionStatusEnum,
    default: ExecutionStatusEnum.SUCCEEDED,
  })
  status: ExecutionStatusEnum;

  @Column({
    name: 'started_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  startedAt: number;

  @Column({ name: 'finished_at', type: 'bigint', nullable: true })
  finishedAt: number;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;
}
