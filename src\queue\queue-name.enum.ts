export enum QueueName {
  EMAIL_SYSTEM = 'email-system',
  EMAIL_MARKETING = 'email-marketing',
  SMS = 'sms',
  SMS_MARKETING = 'sms-marketing',
  AFFILIATE_CLICK = 'affiliate-click',
  FINE_TUNE = 'fine-tune',
  ZALO_WEBHOOK = 'zalo-webhook',
  ZALO_ZNS = 'zalo-zns',
  ZALO_CONSULTATION_SEQUENCE = 'zalo-consultation-sequence',
  ZALO_GROUP_MESSAGE_SEQUENCE = 'zalo-group-message-sequence',
  ZALO_VIDEO_TRACKING = 'zalo-video-tracking',
  ZALO_ARTICLE_SCHEDULER = 'zalo-article-scheduler',
  ZALO_ARTICLE_TRACKING = 'zalo-article-tracking',
  INTEGRATION = 'integration',
  DATA_PROCESS = 'data-process',
  ZALO_AUDIENCE_SYNC = 'zalo-audience-sync',

  // Workflow queues - only real execution
  WORKFLOW_EXECUTION = 'workflow-execution',
  IN_APP_AI = 'in-app-ai',
  WEBHOOK = 'webhook',
  USAGE_DEDUCTION = 'usage-deduction',
  WEBSITE_AI = 'website-ai',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Audience Sync
 */
export enum ZaloAudienceSyncJobName {
  /**
   * Job đồng bộ người dùng Zalo vào audience
   */
  SYNC_ZALO_USERS_TO_AUDIENCE = 'sync-zalo-users-to-audience',

  /**
   * Job đồng bộ tin nhắn Zalo vào database
   */
  SYNC_ZALO_MESSAGES = 'sync-zalo-messages',
}

/**
 * Enum định nghĩa các tên job trong queue Workflow Execution
 */
export enum WorkflowExecutionJobName {
  /**
   * Job thực thi workflow từ trigger
   */
  EXECUTE_WORKFLOW = 'execute-workflow',

  /**
   * Job thực thi node đơn lẻ trong workflow
   */
  EXECUTE_NODE = 'execute-node',

  /**
   * Job retry failed workflow execution
   */
  RETRY_WORKFLOW = 'retry-workflow',

  /**
   * Job cleanup workflow execution data
   */
  CLEANUP_EXECUTION = 'cleanup-execution',
}

// Node Test job names removed - focusing on real execution only

/**
 * Enum định nghĩa các tên job trong queue Zalo Video Tracking
 */
export enum ZaloVideoTrackingJobName {
  /**
   * Job kiểm tra trạng thái video upload
   */
  CHECK_VIDEO_STATUS = 'check-video-status',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Article Scheduler
 */
export enum ZaloArticleSchedulerJobName {
  /**
   * Job xuất bản bài viết đã lên lịch
   */
  PUBLISH_SCHEDULED_ARTICLE = 'publish-scheduled-article',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Article Tracking
 */
export enum ZaloArticleTrackingJobName {
  /**
   * Job kiểm tra tiến trình tạo bài viết và lấy article ID
   */
  CHECK_ARTICLE_STATUS = 'check-article-status',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Consultation Sequence
 */
export enum ZaloConsultationSequenceJobName {
  /**
   * Job gửi chuỗi tin nhắn tư vấn cho 1 user
   */
  SEND_CONSULTATION_SEQUENCE_USER = 'send-consultation-sequence-user',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Group Message Sequence
 */
export enum ZaloGroupMessageSequenceJobName {
  /**
   * Job gửi chuỗi tin nhắn group cho nhiều nhóm
   */
  SEND_GROUP_MESSAGE_SEQUENCE = 'send-group-message-sequence',
}
