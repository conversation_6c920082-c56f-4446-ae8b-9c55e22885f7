import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In } from 'typeorm';
import { ExecutionNodeData } from '../entities/execution-node-data.entity';


/**
 * Repository cho ExecutionNodeData entity
 * Chứa các method CRUD và business logic cho execution node data
 */
@Injectable()
export class ExecutionNodeDataRepository {
  constructor(
    @InjectRepository(ExecutionNodeData)
    private readonly repository: Repository<ExecutionNodeData>,
  ) {}

  /**
   * Tạo execution node data mới
   */
  async create(nodeData: Partial<ExecutionNodeData>): Promise<ExecutionNodeData> {
    const executionNodeData = this.repository.create({
      ...nodeData,
      executedAt: Date.now(),
    });
    return await this.repository.save(executionNodeData);
  }

  /**
   * Tạo nhiều execution node data cùng lúc
   */
  async createMany(nodesData: Partial<ExecutionNodeData>[]): Promise<ExecutionNodeData[]> {
    const executionNodesData = nodesData.map(nodeData => 
      this.repository.create({
        ...nodeData,
        executedAt: Date.now(),
      })
    );
    return await this.repository.save(executionNodesData);
  }

  /**
   * Tìm execution node data theo ID
   */
  async findById(id: number): Promise<ExecutionNodeData | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm nhiều execution node data theo IDs
   */
  async findByIds(ids: number[]): Promise<ExecutionNodeData[]> {
    return await this.repository.find({
      where: { id: In(ids) },
      order: { executedAt: 'ASC' },
    });
  }

  /**
   * Tìm tất cả execution node data của một execution
   */
  async findByExecutionId(executionId: string): Promise<ExecutionNodeData[]> {
    return await this.repository.find({
      where: { executionId },
      order: { executedAt: 'ASC' },
    });
  }

  /**
   * Tìm execution node data theo node name
   */
  async findByNodeName(nodeName: string): Promise<ExecutionNodeData[]> {
    return await this.repository.find({
      where: { nodeName },
      order: { executedAt: 'DESC' },
    });
  }

  /**
   * Tìm execution node data theo execution và node name
   */
  async findByExecutionIdAndNodeName(
    executionId: string,
    nodeName: string,
  ): Promise<ExecutionNodeData | null> {
    return await this.repository.findOne({
      where: { executionId, nodeName },
    });
  }

  /**
   * Tìm execution node data có input data
   */
  async findWithInputData(executionId?: string): Promise<ExecutionNodeData[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('nodeData')
      .where('nodeData.inputData IS NOT NULL');

    if (executionId) {
      queryBuilder.andWhere('nodeData.executionId = :executionId', { executionId });
    }

    return await queryBuilder
      .orderBy('nodeData.executedAt', 'ASC')
      .getMany();
  }

  /**
   * Tìm execution node data có output data
   */
  async findWithOutputData(executionId?: string): Promise<ExecutionNodeData[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('nodeData')
      .where('nodeData.outputData IS NOT NULL');

    if (executionId) {
      queryBuilder.andWhere('nodeData.executionId = :executionId', { executionId });
    }

    return await queryBuilder
      .orderBy('nodeData.executedAt', 'ASC')
      .getMany();
  }

  /**
   * Cập nhật execution node data
   */
  async update(id: number, updateData: Partial<ExecutionNodeData>): Promise<ExecutionNodeData | null> {
    const updateResult = await this.repository.update(id, updateData);

    if (updateResult.affected === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật input data
   */
  async updateInputData(id: number, inputData: any): Promise<ExecutionNodeData | null> {
    return await this.update(id, { inputData });
  }

  /**
   * Cập nhật output data
   */
  async updateOutputData(id: number, outputData: any): Promise<ExecutionNodeData | null> {
    return await this.update(id, { outputData });
  }

  /**
   * Cập nhật input data theo execution và node name
   */
  async updateInputDataByExecutionAndNode(
    executionId: string,
    nodeName: string,
    inputData: any,
  ): Promise<ExecutionNodeData | null> {
    const nodeData = await this.findByExecutionIdAndNodeName(executionId, nodeName);
    if (!nodeData) {
      return null;
    }
    return await this.updateInputData(nodeData.id, inputData);
  }

  /**
   * Cập nhật output data theo execution và node name
   */
  async updateOutputDataByExecutionAndNode(
    executionId: string,
    nodeName: string,
    outputData: any,
  ): Promise<ExecutionNodeData | null> {
    const nodeData = await this.findByExecutionIdAndNodeName(executionId, nodeName);
    if (!nodeData) {
      return null;
    }
    return await this.updateOutputData(nodeData.id, outputData);
  }

  /**
   * Xóa execution node data
   */
  async delete(id: number): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả execution node data của một execution
   */
  async deleteByExecutionId(executionId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ executionId });
    return deleteResult.affected || 0;
  }

  /**
   * Xóa execution node data theo node name
   */
  async deleteByNodeName(nodeName: string): Promise<number> {
    const deleteResult = await this.repository.delete({ nodeName });
    return deleteResult.affected || 0;
  }

  /**
   * Đếm số lượng execution node data
   */
  async count(where?: FindOptionsWhere<ExecutionNodeData>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm execution node data theo execution
   */
  async countByExecutionId(executionId: string): Promise<number> {
    return await this.repository.count({
      where: { executionId },
    });
  }

  /**
   * Đếm execution node data theo node name
   */
  async countByNodeName(nodeName: string): Promise<number> {
    return await this.repository.count({
      where: { nodeName },
    });
  }

  /**
   * Tìm execution node data với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<ExecutionNodeData>,
  ): Promise<{ nodeData: ExecutionNodeData[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [nodeData, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { executedAt: 'DESC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      nodeData,
      total,
      totalPages,
    };
  }

  /**
   * Bulk delete execution node data
   */
  async bulkDelete(ids: number[]): Promise<number> {
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(ExecutionNodeData)
      .whereInIds(ids)
      .execute();

    return deleteResult.affected || 0;
  }

  /**
   * Tìm execution flow (theo thứ tự thực thi)
   */
  async getExecutionFlow(executionId: string): Promise<ExecutionNodeData[]> {
    return await this.repository.find({
      where: { executionId },
      order: { executedAt: 'ASC' },
    });
  }

  /**
   * Tìm node data gần nhất theo node name
   */
  async findLatestByNodeName(nodeName: string): Promise<ExecutionNodeData | null> {
    return await this.repository.findOne({
      where: { nodeName },
      order: { executedAt: 'DESC' },
    });
  }

  /**
   * Tìm execution node data trong khoảng thời gian
   */
  async findByDateRange(startDate: number, endDate: number): Promise<ExecutionNodeData[]> {
    return await this.repository
      .createQueryBuilder('nodeData')
      .where('nodeData.executedAt >= :startDate', { startDate })
      .andWhere('nodeData.executedAt <= :endDate', { endDate })
      .orderBy('nodeData.executedAt', 'ASC')
      .getMany();
  }

  /**
   * Cleanup execution node data cũ
   */
  async cleanupOldNodeData(olderThanDays: number): Promise<number> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(ExecutionNodeData)
      .where('executedAt < :cutoffDate', { cutoffDate })
      .execute();

    return deleteResult.affected || 0;
  }
}
